import asyncio
import logging
from dotenv import load_dotenv
from agent import ProactiveAgent
from websocket_server import start_websocket_server
from task_mcp import TaskMCPServer
from email_mcp import EmailMCPServer
from biz_stats_mcp import BizStatsMCPServer

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("JarvisDemo")

async def main():
    """Main application loop to initialize MCP servers, agent, and WebSocket server."""
    # Initialize MCP servers
    task_mcp = TaskMCPServer()
    email_mcp = EmailMCPServer()
    biz_stats_mcp = BizStatsMCPServer()
    
    # Start MCP servers with WebSocket support
    await task_mcp.start()
    await email_mcp.start()
    await biz_stats_mcp.start()
    
    # Create the agent with MCP tools and WebSocket subscriptions
    agent = await agent.create_agent(task_mcp, email_mcp, biz_stats_mcp)
    
    # Start WebSocket server for user notifications
    websocket_task = asyncio.create_task(start_websocket_server(agent))
    
    # Start WebSocket listeners for MCP servers
    await asyncio.gather(
        task_mcp.start_websocket_listener(agent),
        email_mcp.start_websocket_listener(agent),
        biz_stats_mcp.start_websocket_listener(agent)
    )
    
    # Keep the application running
    await asyncio.Future()

if __name__ == "__main__":
    asyncio.run(main())