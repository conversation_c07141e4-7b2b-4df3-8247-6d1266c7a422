#!/usr/bin/env python3
"""
Autonomous Agentic AI System

A scalable, autonomous AI system that uses BeeAI framework and WebSocket communication
with MCP servers for fully autonomous agentic behavior.

Usage:
    python main.py

The system will:
1. Load configuration from config.yaml
2. Initialize MCP servers (Task, Email, Business Stats)
3. Create proactive AI agents
4. Start WebSocket servers for real-time communication
5. Provide autonomous monitoring and assistance

Features:
- Fully autonomous behavior with configurable triggers
- Scalable architecture for adding new agents and MCP servers
- Real-time WebSocket communication
- Comprehensive error handling and logging
- Graceful shutdown handling
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent))

from core.orchestrator import main

if __name__ == "__main__":
    print("🤖 Starting Autonomous Agentic AI System...")
    print("📋 Loading configuration and initializing components...")
    print("🔗 Setting up WebSocket communication channels...")
    print("🧠 Creating AI agents with autonomous behavior...")
    print()

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye! System shutdown complete.")
    except Exception as e:
        print(f"\n❌ System error: {e}")
        sys.exit(1)