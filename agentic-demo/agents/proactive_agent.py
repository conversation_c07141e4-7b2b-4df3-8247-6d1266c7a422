"""Proactive AI agent with autonomous behavior."""

import asyncio
import json
import statistics
import websockets
import logging
from typing import Dict, Any, List
from beeai_framework.agents.react.agent import ReActAgent
from beeai_framework.backend.chat import Chat<PERSON>odel, ChatModelParameters
from beeai_framework.memory.token_memory import TokenMemory

from core.base import BaseAgent, BaseMCPServer
from core.config import config

logger = logging.getLogger(__name__)

class ProactiveAgent(BaseAgent):
    """Proactive AI agent that monitors MCP servers and provides autonomous assistance."""
    
    def __init__(self, name: str, agent_config: Dict[str, Any]):
        super().__init__(name, agent_config)
        self.llm_agent = None
        self.seen_tasks = set()
        self.seen_emails = set()
        self.seen_stats = set()
        self.autonomous_config = agent_config.get("config", {})
        self.websocket_listeners = {}
    
    async def initialize(self) -> None:
        """Initialize the proactive agent."""
        logger.info(f"Initializing proactive agent: {self.name}")
    
    async def create_llm_agent(self) -> None:
        """Create the underlying LLM agent."""
        try:
            llm_config = config.get_llm_config()
            llm = ChatModel.from_name(
                f"{llm_config['provider']}:{llm_config['model']}", 
                ChatModelParameters(temperature=llm_config.get('temperature', 0.1))
            )
            
            # Collect all tools from MCP servers
            all_tools = []
            for server in self.mcp_servers.values():
                tools = await server.get_tools()
                all_tools.extend(tools)
            
            self.llm_agent = ReActAgent(
                llm=llm,
                tools=all_tools,
                memory=TokenMemory(llm)
            )
            
            logger.info(f"Created LLM agent with {len(all_tools)} tools")
            
        except Exception as e:
            logger.error(f"Failed to create LLM agent: {e}")
            raise
    
    async def start_websocket_listeners(self) -> None:
        """Start WebSocket listeners for all MCP servers."""
        retry_config = self.autonomous_config.get("websocket_retry", {})
        max_retries = retry_config.get("max_retries", 5)
        retry_delay = retry_config.get("retry_delay", 2)
        backoff_multiplier = retry_config.get("backoff_multiplier", 1.5)
        
        for server_name, server in self.mcp_servers.items():
            task = asyncio.create_task(
                self._websocket_listener_with_retry(
                    server_name, server.websocket_port, max_retries, retry_delay, backoff_multiplier
                )
            )
            self._tasks.append(task)
            self.websocket_listeners[server_name] = task
    
    async def _websocket_listener_with_retry(self, server_name: str, port: int, max_retries: int, retry_delay: float, backoff_multiplier: float) -> None:
        """WebSocket listener with retry logic."""
        retries = 0
        current_delay = retry_delay
        
        while retries < max_retries and self.is_running:
            try:
                await self._websocket_listener(server_name, port)
                # If we get here, connection was successful, reset retry count
                retries = 0
                current_delay = retry_delay
            except Exception as e:
                retries += 1
                if retries >= max_retries:
                    logger.error(f"Max retries reached for {server_name} WebSocket listener: {e}")
                    break
                
                logger.warning(f"WebSocket connection failed for {server_name} (attempt {retries}/{max_retries}): {e}")
                await asyncio.sleep(current_delay)
                current_delay *= backoff_multiplier
    
    async def _websocket_listener(self, server_name: str, port: int) -> None:
        """Listen to a specific MCP server's WebSocket."""
        uri = f"ws://localhost:{port}"
        
        async with websockets.connect(uri) as websocket:
            logger.info(f"Connected to {server_name} WebSocket at {uri}")
            
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_notification(server_name, data)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON from {server_name}: {message}")
                except Exception as e:
                    logger.error(f"Error handling message from {server_name}: {e}")
    
    async def handle_notification(self, server_name: str, data: Dict[str, Any]) -> None:
        """Handle notifications from MCP servers."""
        notification_type = data.get("type")
        notification_data = data.get("data", {})
        
        logger.info(f"Received notification from {server_name}: {notification_type}")
        
        if notification_type == "task":
            await self._handle_task_notification(notification_data)
        elif notification_type == "email":
            await self._handle_email_notification(notification_data)
        elif notification_type == "stats":
            await self._handle_stats_notification(notification_data)
    
    async def _handle_task_notification(self, task_data: Dict[str, Any]) -> None:
        """Handle task notifications."""
        task_id = task_data.get("task_id")
        assignee = task_data.get("assignee")
        description = task_data.get("description")
        
        if task_id not in self.seen_tasks:
            self.seen_tasks.add(task_id)
            
            # Check autonomous triggers
            triggers = self.autonomous_config.get("autonomous_triggers", [])
            for trigger in triggers:
                if trigger.get("type") == "new_task" and assignee == "user":
                    await self.notify_users(f"New task assigned: {description}")
                    break
    
    async def _handle_email_notification(self, email_data: Dict[str, Any]) -> None:
        """Handle email notifications."""
        email_id = email_data.get("email_id")
        subject = email_data.get("subject")
        
        if email_id not in self.seen_emails:
            self.seen_emails.add(email_id)
            
            # Check autonomous triggers
            triggers = self.autonomous_config.get("autonomous_triggers", [])
            for trigger in triggers:
                if trigger.get("type") == "new_email":
                    await self.notify_users(f"New email received: {subject}. Would you like a summary? (yes/no)")
                    break
    
    async def _handle_stats_notification(self, stats_data: Dict[str, Any]) -> None:
        """Handle stats notifications."""
        month = stats_data.get("month")
        revenue = stats_data.get("revenue")
        
        if month not in self.seen_stats:
            self.seen_stats.add(month)
            
            # Check for revenue decline
            if await self._check_revenue_decline(revenue):
                await self.notify_users(f"⚠️ Revenue decline detected for {month}. Would you like an analysis report? (yes/no)")
            else:
                await self.notify_users(f"New business stats received for {month}. Would you like an analysis report? (yes/no)")
    
    async def _check_revenue_decline(self, current_revenue: float) -> bool:
        """Check if there's a revenue decline."""
        try:
            # Get stats from the biz stats server
            biz_stats_server = self.mcp_servers.get("BizStatsMCPServer")
            if not biz_stats_server:
                return False
            
            stats = await biz_stats_server.get_stats()
            if len(stats) < 2:
                return False
            
            # Get the previous revenue
            previous_revenue = stats[-2].get("revenue", 0)
            
            # Check if current revenue is 5% less than previous
            return current_revenue < previous_revenue * 0.95
            
        except Exception as e:
            logger.error(f"Error checking revenue decline: {e}")
            return False
    
    async def generate_stats_report(self) -> str:
        """Generate a comprehensive business statistics report."""
        try:
            biz_stats_server = self.mcp_servers.get("BizStatsMCPServer")
            if not biz_stats_server:
                return "Business stats server not available."
            
            stats = await biz_stats_server.get_stats()
            if not stats:
                return "No business statistics available."
            
            revenues = [s["revenue"] for s in stats]
            if len(revenues) >= 3:
                forecast = statistics.mean(revenues[-3:])  # 3-month moving average
            else:
                forecast = revenues[-1] if revenues else 0
            
            decline_reasons = []
            for i in range(1, len(revenues)):
                if revenues[i] < revenues[i-1]:
                    decline_reasons.append(f"Decline in {stats[i]['month']}: Revenue dropped from {revenues[i-1]} to {revenues[i]}.")
            
            prompt = f"""
            Analyze the following business statistics and provide a comprehensive report:
            {json.dumps(stats, indent=2)}
            
            Please provide:
            1. Revenue forecast for next month based on 3-month moving average (${forecast:.2f})
            2. Analysis of any revenue declines
            3. Specific recommendations to improve revenue
            4. Key insights and trends
            
            Format the response as a clear, actionable business report.
            """
            
            response = await self.llm_agent.run(prompt)
            report = response.get("final_answer", "Report generation failed.")
            
            return f"""
# Business Statistics Report

## Revenue Forecast
Next month's revenue forecast (3-month moving average): ${forecast:.2f}

## Revenue Decline Analysis
{chr(10).join(decline_reasons) or "No significant declines detected."}

## AI Analysis and Recommendations
{report}
            """
            
        except Exception as e:
            logger.error(f"Error generating stats report: {e}")
            return f"Error generating report: {e}"
