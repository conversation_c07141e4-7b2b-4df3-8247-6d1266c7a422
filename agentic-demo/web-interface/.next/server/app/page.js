/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/workspace/agentic-demo/web-interface/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYmxvY2tiYXNlJTJGd29ya3NwYWNlJTJGYWdlbnRpYy1kZW1vJTJGd2ViLWludGVyZmFjZSUyRmFwcCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBdUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2VudGljLWFpLXdlYi1pbnRlcmZhY2UvP2M0YTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYmxvY2tiYXNlL3dvcmtzcGFjZS9hZ2VudGljLWRlbW8vd2ViLWludGVyZmFjZS9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/ChatInterface.tsx":
/*!******************************************!*\
  !*** ./app/components/ChatInterface.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \n\n\nfunction ChatInterface({ messages, onSendMessage, isConnected, isTyping = false, className = \"\" }) {\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, [\n        messages,\n        isTyping\n    ]);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (inputValue.trim() && isConnected) {\n            onSendMessage(inputValue.trim());\n            setInputValue(\"\");\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const formatTime = (date)=>{\n        return new Intl.DateTimeFormat(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        }).format(date);\n    };\n    const getMessageIcon = (type)=>{\n        switch(type){\n            case \"ai\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    size: 20,\n                    className: \"text-primary-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, this);\n            case \"user\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    size: 20,\n                    className: \"text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, this);\n            case \"system\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    size: 20,\n                    className: \"text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getMessageStyle = (type)=>{\n        switch(type){\n            case \"ai\":\n                return \"bg-white border border-gray-200\";\n            case \"user\":\n                return \"bg-primary-500 text-white ml-auto\";\n            case \"system\":\n                return \"bg-gray-100 text-gray-600 text-center\";\n            default:\n                return \"bg-gray-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex flex-col h-full ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b border-gray-200 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-primary-100 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                size: 24,\n                                className: \"text-primary-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900\",\n                                    children: \"AI Assistant\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: isConnected ? \"Online and ready to help\" : \"Connecting...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50\",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                size: 48,\n                                className: \"mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mb-2\",\n                                children: \"Welcome to the AI Assistant!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-400\",\n                                children: \"Ask me about tasks, emails, business stats, or request reports.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this) : messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex items-start gap-3 message-enter ${message.type === \"user\" ? \"flex-row-reverse\" : \"\"}`,\n                            children: [\n                                message.type !== \"system\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 p-2 rounded-full bg-gray-100\",\n                                    children: getMessageIcon(message.type)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${getMessageStyle(message.type)}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm whitespace-pre-wrap\",\n                                            children: message.content\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: `text-xs mt-1 ${message.type === \"user\" ? \"text-primary-200\" : \"text-gray-500\"}`,\n                                            children: [\n                                                formatTime(message.timestamp),\n                                                message.status === \"sending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        size: 12,\n                                                        className: \"inline animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 21\n                                                }, this),\n                                                message.status === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-danger-500\",\n                                                    children: \"Failed\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)),\n                    isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0 p-2 rounded-full bg-gray-100\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    size: 20,\n                                    className: \"text-primary-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 px-4 py-2 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.1s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.2s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 ml-2\",\n                                            children: \"AI is thinking...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200 bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: inputRef,\n                                type: \"text\",\n                                value: inputValue,\n                                onChange: (e)=>setInputValue(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                placeholder: isConnected ? \"Ask me about tasks, emails, or business stats...\" : \"Connecting to AI...\",\n                                disabled: !isConnected,\n                                className: \"flex-1 input\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: !inputValue.trim() || !isConnected,\n                                className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 mt-2\",\n                        children: \"Press Enter to send, Shift+Enter for new line\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ChatInterface.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ChatInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ConnectionStatus.tsx":
/*!*********************************************!*\
  !*** ./app/components/ConnectionStatus.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionStatus: () => (/* binding */ ConnectionStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Wifi,WifiOff!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* __next_internal_client_entry_do_not_use__ ConnectionStatus auto */ \n\nfunction ConnectionStatus({ status, className = \"\" }) {\n    const getStatusConfig = ()=>{\n        switch(status){\n            case \"connected\":\n                return {\n                    icon: _barrel_optimize_names_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n                    text: \"Connected\",\n                    className: \"text-success-600 bg-success-50 border-success-200\",\n                    indicatorClass: \"bg-success-500 connection-indicator connected\"\n                };\n            case \"connecting\":\n                return {\n                    icon: _barrel_optimize_names_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                    text: \"Connecting...\",\n                    className: \"text-warning-600 bg-warning-50 border-warning-200\",\n                    indicatorClass: \"bg-warning-500 connection-indicator connecting\",\n                    spinning: true\n                };\n            case \"disconnected\":\n                return {\n                    icon: _barrel_optimize_names_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                    text: \"Disconnected\",\n                    className: \"text-danger-600 bg-danger-50 border-danger-200\",\n                    indicatorClass: \"bg-danger-500 connection-indicator disconnected\"\n                };\n            case \"error\":\n                return {\n                    icon: _barrel_optimize_names_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                    text: \"Connection Error\",\n                    className: \"text-danger-600 bg-danger-50 border-danger-200\",\n                    indicatorClass: \"bg-danger-500 connection-indicator disconnected\"\n                };\n            default:\n                return {\n                    icon: _barrel_optimize_names_Loader2_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                    text: \"Unknown\",\n                    className: \"text-gray-600 bg-gray-50 border-gray-200\",\n                    indicatorClass: \"bg-gray-500\"\n                };\n        }\n    };\n    const config = getStatusConfig();\n    const Icon = config.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `inline-flex items-center gap-2 px-3 py-1.5 rounded-full border text-sm font-medium ${config.className} ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `w-2 h-2 rounded-full ${config.indicatorClass}`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ConnectionStatus.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ConnectionStatus.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                size: 16,\n                className: config.spinning ? \"animate-spin\" : \"\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ConnectionStatus.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: config.text\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ConnectionStatus.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/ConnectionStatus.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ConnectionStatus.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/Dashboard.tsx":
/*!**************************************!*\
  !*** ./app/components/Dashboard.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dashboard: () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,CheckSquare,DollarSign,Mail,RefreshCw,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,CheckSquare,DollarSign,Mail,RefreshCw,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,CheckSquare,DollarSign,Mail,RefreshCw,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,CheckSquare,DollarSign,Mail,RefreshCw,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,CheckSquare,DollarSign,Mail,RefreshCw,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,CheckSquare,DollarSign,Mail,RefreshCw,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,CheckSquare,DollarSign,Mail,RefreshCw,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Calendar,CheckSquare,DollarSign,Mail,RefreshCw,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* __next_internal_client_entry_do_not_use__ Dashboard auto */ \n\n\nfunction Dashboard({ data, onRefresh, isLoading = false, className = \"\" }) {\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"tasks\");\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\",\n            minimumFractionDigits: 0\n        }).format(amount);\n    };\n    const formatDate = (dateString)=>{\n        return new Intl.DateTimeFormat(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\"\n        }).format(new Date(dateString + \"-01\"));\n    };\n    const getTasksByAssignee = ()=>{\n        const userTasks = data.tasks.filter((task)=>task.assignee === \"user\").length;\n        const otherTasks = data.tasks.filter((task)=>task.assignee !== \"user\").length;\n        return {\n            userTasks,\n            otherTasks\n        };\n    };\n    const getRevenueGrowth = ()=>{\n        if (data.stats.length < 2) return 0;\n        const latest = data.stats[data.stats.length - 1];\n        const previous = data.stats[data.stats.length - 2];\n        return (latest.revenue - previous.revenue) / previous.revenue * 100;\n    };\n    const getTotalRevenue = ()=>{\n        return data.stats.reduce((sum, stat)=>sum + stat.revenue, 0);\n    };\n    const { userTasks, otherTasks } = getTasksByAssignee();\n    const revenueGrowth = getRevenueGrowth();\n    const totalRevenue = getTotalRevenue();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `space-y-6 ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"Last updated: \",\n                                    data.lastUpdated.toLocaleTimeString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onRefresh,\n                        disabled: isLoading,\n                        className: \"btn-secondary flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                size: 16,\n                                className: isLoading ? \"animate-spin\" : \"\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            \"Refresh\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Tasks\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: data.tasks.length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: [\n                                                userTasks,\n                                                \" assigned to you\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-primary-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"text-primary-600\",\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Emails\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: data.emails.length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Recent communications\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-success-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"text-success-600\",\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Revenue\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatCurrency(totalRevenue)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Across all periods\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-warning-100 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"text-warning-600\",\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Revenue Growth\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: `text-2xl font-bold ${revenueGrowth >= 0 ? \"text-success-600\" : \"text-danger-600\"}`,\n                                            children: [\n                                                revenueGrowth >= 0 ? \"+\" : \"\",\n                                                revenueGrowth.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Month over month\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-3 rounded-lg ${revenueGrowth >= 0 ? \"bg-success-100\" : \"bg-danger-100\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: `${revenueGrowth >= 0 ? \"text-success-600\" : \"text-danger-600\"}`,\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex space-x-8\",\n                            children: [\n                                {\n                                    id: \"tasks\",\n                                    label: \"Tasks\",\n                                    icon: _barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n                                },\n                                {\n                                    id: \"emails\",\n                                    label: \"Emails\",\n                                    icon: _barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                                },\n                                {\n                                    id: \"stats\",\n                                    label: \"Business Stats\",\n                                    icon: _barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                                }\n                            ].map(({ id, label, icon: Icon })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedTab(id),\n                                    className: `flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${selectedTab === id ? \"border-primary-500 text-primary-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this),\n                                        label\n                                    ]\n                                }, id, true, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    selectedTab === \"tasks\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: data.tasks.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-center py-8\",\n                            children: \"No tasks available\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 15\n                        }, this) : data.tasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                size: 20,\n                                                className: \"text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: task.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Task #\",\n                                                            task.task_id\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `badge ${task.assignee === \"user\" ? \"badge-primary\" : \"badge-secondary\"}`,\n                                                children: task.assignee\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, task.task_id, true, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    selectedTab === \"emails\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: data.emails.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-center py-8\",\n                            children: \"No emails available\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 15\n                        }, this) : data.emails.map((email)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start gap-3 p-4 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 20,\n                                        className: \"text-gray-400 mt-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: email.subject\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 21\n                                            }, this),\n                                            email.body && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1 line-clamp-2\",\n                                                children: email.body\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-2\",\n                                                children: [\n                                                    \"Email #\",\n                                                    email.email_id\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, email.email_id, true, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this),\n                    selectedTab === \"stats\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: data.stats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-center py-8\",\n                            children: \"No business stats available\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 15\n                        }, this) : data.stats.map((stat, index)=>{\n                            const prevStat = index > 0 ? data.stats[index - 1] : null;\n                            const growth = prevStat ? (stat.revenue - prevStat.revenue) / prevStat.revenue * 100 : 0;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Calendar_CheckSquare_DollarSign_Mail_RefreshCw_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 20,\n                                                className: \"text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: formatDate(stat.month)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Sales Volume: \",\n                                                            stat.sales_volume.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-bold text-gray-900\",\n                                                children: formatCurrency(stat.revenue)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 23\n                                            }, this),\n                                            prevStat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `text-sm ${growth >= 0 ? \"text-success-600\" : \"text-danger-600\"}`,\n                                                children: [\n                                                    growth >= 0 ? \"+\" : \"\",\n                                                    growth.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, stat.month, true, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 19\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/Dashboard.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Dashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/NotificationPanel.tsx":
/*!**********************************************!*\
  !*** ./app/components/NotificationPanel.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationPanel: () => (/* binding */ NotificationPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Bell_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Bell,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ NotificationPanel auto */ \n\n\nfunction NotificationPanel({ notifications, onMarkAsRead, onClearAll, className = \"\" }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    // Auto-open panel when new notifications arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (unreadCount > 0) {\n            setIsOpen(true);\n        }\n    }, [\n        unreadCount\n    ]);\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"text-success-500\",\n                    size: 20\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"text-danger-500\",\n                    size: 20\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 16\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"text-warning-500\",\n                    size: 20\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"text-primary-500\",\n                    size: 20\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getNotificationStyle = (type)=>{\n        switch(type){\n            case \"success\":\n                return \"border-l-success-500 bg-success-50\";\n            case \"error\":\n                return \"border-l-danger-500 bg-danger-50\";\n            case \"warning\":\n                return \"border-l-warning-500 bg-warning-50\";\n            default:\n                return \"border-l-primary-500 bg-primary-50\";\n        }\n    };\n    const formatTime = (date)=>{\n        return new Intl.DateTimeFormat(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        }).format(date);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 24,\n                        className: \"text-gray-600\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-danger-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse\",\n                        children: unreadCount > 9 ? \"9+\" : unreadCount\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 top-full mt-2 w-96 bg-white rounded-xl shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: [\n                                    \"AI Notifications\",\n                                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm text-gray-500\",\n                                        children: [\n                                            \"(\",\n                                            unreadCount,\n                                            \" unread)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClearAll,\n                                        className: \"text-sm text-gray-500 hover:text-gray-700\",\n                                        children: \"Clear All\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsOpen(false),\n                                        className: \"p-1 rounded hover:bg-gray-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16,\n                                            className: \"text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-80 overflow-y-auto\",\n                        children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Bell_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 48,\n                                    className: \"mx-auto mb-4 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No notifications yet\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"AI will notify you when new events occur\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-100\",\n                            children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-4 border-l-4 ${getNotificationStyle(notification.type)} ${!notification.read ? \"bg-opacity-100\" : \"bg-opacity-50\"} hover:bg-opacity-75 transition-all cursor-pointer`,\n                                    onClick: ()=>onMarkAsRead(notification.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3\",\n                                        children: [\n                                            getNotificationIcon(notification.type),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: `text-sm ${!notification.read ? \"font-medium\" : \"\"} text-gray-900`,\n                                                        children: notification.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: formatTime(notification.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 23\n                                            }, this),\n                                            !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-primary-500 rounded-full flex-shrink-0 mt-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 21\n                                    }, this)\n                                }, notification.id, false, {\n                                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>setIsOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/components/NotificationPanel.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/NotificationPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./app/hooks/useWebSocket.ts":
/*!***********************************!*\
  !*** ./app/hooks/useWebSocket.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useWebSocket auto */ \nfunction useWebSocket({ url, reconnectAttempts = 5, reconnectInterval = 3000, onMessage, onConnect, onDisconnect, onError }) {\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"disconnected\");\n    const [lastMessage, setLastMessage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [messageHistory, setMessageHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const ws = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectCount = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const shouldReconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (ws.current?.readyState === WebSocket.OPEN) {\n            return;\n        }\n        setConnectionStatus(\"connecting\");\n        try {\n            ws.current = new WebSocket(url);\n            ws.current.onopen = ()=>{\n                console.log(\"WebSocket connected\");\n                setConnectionStatus(\"connected\");\n                reconnectCount.current = 0;\n                onConnect?.();\n            };\n            ws.current.onmessage = (event)=>{\n                try {\n                    const message = JSON.parse(event.data);\n                    setLastMessage(message);\n                    setMessageHistory((prev)=>[\n                            ...prev.slice(-99),\n                            message\n                        ]); // Keep last 100 messages\n                    onMessage?.(message);\n                } catch (error) {\n                    console.error(\"Failed to parse WebSocket message:\", error);\n                }\n            };\n            ws.current.onclose = ()=>{\n                console.log(\"WebSocket disconnected\");\n                setConnectionStatus(\"disconnected\");\n                onDisconnect?.();\n                // Attempt to reconnect if enabled and within retry limit\n                if (shouldReconnect.current && reconnectCount.current < reconnectAttempts) {\n                    reconnectCount.current++;\n                    console.log(`Attempting to reconnect (${reconnectCount.current}/${reconnectAttempts})...`);\n                    reconnectTimeoutId.current = setTimeout(()=>{\n                        connect();\n                    }, reconnectInterval * Math.pow(1.5, reconnectCount.current - 1)); // Exponential backoff\n                }\n            };\n            ws.current.onerror = (error)=>{\n                console.error(\"WebSocket error:\", error);\n                setConnectionStatus(\"error\");\n                onError?.(error);\n            };\n        } catch (error) {\n            console.error(\"Failed to create WebSocket connection:\", error);\n            setConnectionStatus(\"error\");\n        }\n    }, [\n        url,\n        reconnectAttempts,\n        reconnectInterval,\n        onMessage,\n        onConnect,\n        onDisconnect,\n        onError\n    ]);\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        shouldReconnect.current = false;\n        if (reconnectTimeoutId.current) {\n            clearTimeout(reconnectTimeoutId.current);\n            reconnectTimeoutId.current = null;\n        }\n        if (ws.current) {\n            ws.current.close();\n            ws.current = null;\n        }\n        setConnectionStatus(\"disconnected\");\n    }, []);\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((message)=>{\n        if (ws.current?.readyState === WebSocket.OPEN) {\n            try {\n                ws.current.send(JSON.stringify(message));\n                return true;\n            } catch (error) {\n                console.error(\"Failed to send WebSocket message:\", error);\n                return false;\n            }\n        } else {\n            console.warn(\"WebSocket is not connected\");\n            return false;\n        }\n    }, []);\n    const clearHistory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setMessageHistory([]);\n        setLastMessage(null);\n    }, []);\n    // Auto-connect on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        connect();\n        return ()=>{\n            shouldReconnect.current = false;\n            if (reconnectTimeoutId.current) {\n                clearTimeout(reconnectTimeoutId.current);\n            }\n            if (ws.current) {\n                ws.current.close();\n            }\n        };\n    }, [\n        connect\n    ]);\n    return {\n        connectionStatus,\n        lastMessage,\n        messageHistory,\n        sendMessage,\n        connect,\n        disconnect,\n        clearHistory,\n        isConnected: connectionStatus === \"connected\",\n        isConnecting: connectionStatus === \"connecting\"\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/hooks/useWebSocket.ts\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hooks/useWebSocket */ \"(ssr)/./app/hooks/useWebSocket.ts\");\n/* harmony import */ var _components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ConnectionStatus */ \"(ssr)/./app/components/ConnectionStatus.tsx\");\n/* harmony import */ var _components_NotificationPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/NotificationPanel */ \"(ssr)/./app/components/NotificationPanel.tsx\");\n/* harmony import */ var _components_ChatInterface__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/ChatInterface */ \"(ssr)/./app/components/ChatInterface.tsx\");\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/Dashboard */ \"(ssr)/./app/components/Dashboard.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,MessageSquare!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,MessageSquare!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bot_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bot,MessageSquare!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Home() {\n    // State management\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [chatMessages, setChatMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        tasks: [],\n        emails: [],\n        stats: [],\n        lastUpdated: new Date()\n    });\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dashboard\");\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // WebSocket connection to AI system\n    const { connectionStatus, sendMessage, isConnected } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_2__.useWebSocket)({\n        url: \"ws://localhost:8765\",\n        onMessage: handleWebSocketMessage,\n        onConnect: ()=>{\n            addSystemMessage(\"Connected to AI system\");\n            // Load initial data\n            loadDashboardData();\n        },\n        onDisconnect: ()=>{\n            addSystemMessage(\"Disconnected from AI system\");\n        },\n        onError: ()=>{\n            addSystemMessage(\"Connection error occurred\");\n        }\n    });\n    // Handle incoming WebSocket messages\n    function handleWebSocketMessage(message) {\n        console.log(\"Received message:\", message);\n        switch(message.type){\n            case \"notification\":\n                addNotification(\"notification\", message.message || \"New notification\");\n                break;\n            case \"tasks\":\n                setDashboardData((prev)=>({\n                        ...prev,\n                        tasks: message.data || [],\n                        lastUpdated: new Date()\n                    }));\n                break;\n            case \"emails\":\n                setDashboardData((prev)=>({\n                        ...prev,\n                        emails: message.data || [],\n                        lastUpdated: new Date()\n                    }));\n                break;\n            case \"stats\":\n                setDashboardData((prev)=>({\n                        ...prev,\n                        stats: message.data || [],\n                        lastUpdated: new Date()\n                    }));\n                break;\n            case \"report\":\n                addAIMessage(message.data || \"Report generated successfully\");\n                break;\n            case \"error\":\n                addNotification(\"error\", message.message || \"An error occurred\");\n                break;\n            default:\n                console.log(\"Unknown message type:\", message.type);\n        }\n    }\n    // Add notification\n    const addNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((type, message)=>{\n        const notification = {\n            id: Date.now().toString(),\n            type,\n            message,\n            timestamp: new Date(),\n            read: false\n        };\n        setNotifications((prev)=>[\n                notification,\n                ...prev\n            ].slice(0, 50)); // Keep last 50\n    }, []);\n    // Add chat messages\n    const addUserMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        const message = {\n            id: Date.now().toString(),\n            type: \"user\",\n            content,\n            timestamp: new Date(),\n            status: \"sending\"\n        };\n        setChatMessages((prev)=>[\n                ...prev,\n                message\n            ]);\n        return message.id;\n    }, []);\n    const addAIMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        const message = {\n            id: Date.now().toString(),\n            type: \"ai\",\n            content,\n            timestamp: new Date()\n        };\n        setChatMessages((prev)=>[\n                ...prev,\n                message\n            ]);\n        setIsTyping(false);\n    }, []);\n    const addSystemMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        const message = {\n            id: Date.now().toString(),\n            type: \"system\",\n            content,\n            timestamp: new Date()\n        };\n        setChatMessages((prev)=>[\n                ...prev,\n                message\n            ]);\n    }, []);\n    // Handle sending chat messages\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        if (!isConnected) return;\n        const messageId = addUserMessage(content);\n        setIsTyping(true);\n        // Determine message type based on content\n        let requestType = \"chat\";\n        if (content.toLowerCase().includes(\"task\")) {\n            requestType = \"list_tasks\";\n        } else if (content.toLowerCase().includes(\"email\")) {\n            requestType = \"list_emails\";\n        } else if (content.toLowerCase().includes(\"stats\") || content.toLowerCase().includes(\"business\")) {\n            requestType = \"get_stats\";\n        } else if (content.toLowerCase().includes(\"report\")) {\n            requestType = \"generate_stats_report\";\n        }\n        const success = sendMessage({\n            type: requestType,\n            data: {\n                message: content\n            }\n        });\n        if (!success) {\n            // Update message status to error\n            setChatMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                        ...msg,\n                        status: \"error\"\n                    } : msg));\n            setIsTyping(false);\n        } else {\n            // Update message status to sent\n            setChatMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                        ...msg,\n                        status: \"sent\"\n                    } : msg));\n        }\n    }, [\n        isConnected,\n        sendMessage,\n        addUserMessage\n    ]);\n    // Load dashboard data\n    const loadDashboardData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!isConnected) return;\n        sendMessage({\n            type: \"list_tasks\"\n        });\n        sendMessage({\n            type: \"list_emails\"\n        });\n        sendMessage({\n            type: \"get_stats\"\n        });\n    }, [\n        isConnected,\n        sendMessage\n    ]);\n    // Mark notification as read\n    const markNotificationAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{\n        setNotifications((prev)=>prev.map((notification)=>notification.id === id ? {\n                    ...notification,\n                    read: true\n                } : notification));\n    }, []);\n    // Clear all notifications\n    const clearAllNotifications = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setNotifications([]);\n    }, []);\n    // Auto-refresh dashboard data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isConnected) return;\n        const interval = setInterval(()=>{\n            loadDashboardData();\n        }, 30000); // Refresh every 30 seconds\n        return ()=>clearInterval(interval);\n    }, [\n        isConnected,\n        loadDashboardData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200 sticky top-0 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-primary-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 24,\n                                            className: \"text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-gray-900\",\n                                                children: \"Autonomous Agentic AI\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Real-time AI Assistant Interface\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(\"dashboard\"),\n                                        className: `flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${activeTab === \"dashboard\" ? \"bg-primary-100 text-primary-700\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-100\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(\"chat\"),\n                                        className: `flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${activeTab === \"chat\" ? \"bg-primary-100 text-primary-700\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-100\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bot_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Chat\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_3__.ConnectionStatus, {\n                                        status: connectionStatus\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationPanel__WEBPACK_IMPORTED_MODULE_4__.NotificationPanel, {\n                                        notifications: notifications,\n                                        onMarkAsRead: markNotificationAsRead,\n                                        onClearAll: clearAllNotifications\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: activeTab === \"dashboard\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_6__.Dashboard, {\n                    data: dashboardData,\n                    onRefresh: loadDashboardData,\n                    isLoading: !isConnected\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 h-[600px]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterface__WEBPACK_IMPORTED_MODULE_5__.ChatInterface, {\n                            messages: chatMessages,\n                            onSendMessage: handleSendMessage,\n                            isConnected: isConnected,\n                            isTyping: isTyping\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"74954e13dabf\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2VudGljLWFpLXdlYi1pbnRlcmZhY2UvLi9hcHAvZ2xvYmFscy5jc3M/MTJjMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjc0OTU0ZTEzZGFiZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"Autonomous Agentic AI System\",\n    description: \"Real-time web interface for interacting with autonomous AI agents\",\n    keywords: [\n        \"AI\",\n        \"Autonomous\",\n        \"Agentic\",\n        \"WebSocket\",\n        \"Real-time\"\n    ],\n    authors: [\n        {\n            name: \"Agentic AI Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#3b82f6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/agentic-demo/web-interface/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDc0I7QUFFZixNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7UUFBQztRQUFNO1FBQWM7UUFBVztRQUFhO0tBQVk7SUFDbkVDLFNBQVM7UUFBQztZQUFFQyxNQUFNO1FBQWtCO0tBQUU7SUFDdENDLFVBQVU7QUFDWixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7OzBCQUNULDhEQUFDQzs7a0NBQ0MsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7a0NBQ3RCLDhEQUFDQzt3QkFBS1YsTUFBSzt3QkFBY1csU0FBUTs7Ozs7Ozs7Ozs7OzBCQUVuQyw4REFBQ0M7Z0JBQUtDLFdBQVU7MEJBQ2JWOzs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL2FnZW50aWMtYWktd2ViLWludGVyZmFjZS8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnQXV0b25vbW91cyBBZ2VudGljIEFJIFN5c3RlbScsXG4gIGRlc2NyaXB0aW9uOiAnUmVhbC10aW1lIHdlYiBpbnRlcmZhY2UgZm9yIGludGVyYWN0aW5nIHdpdGggYXV0b25vbW91cyBBSSBhZ2VudHMnLFxuICBrZXl3b3JkczogWydBSScsICdBdXRvbm9tb3VzJywgJ0FnZW50aWMnLCAnV2ViU29ja2V0JywgJ1JlYWwtdGltZSddLFxuICBhdXRob3JzOiBbeyBuYW1lOiAnQWdlbnRpYyBBSSBUZWFtJyB9XSxcbiAgdmlld3BvcnQ6ICd3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGhlYWQ+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cInRoZW1lLWNvbG9yXCIgY29udGVudD1cIiMzYjgyZjZcIiAvPlxuICAgICAgPC9oZWFkPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiYW50aWFsaWFzZWRcIj5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiYXV0aG9ycyIsIm5hbWUiLCJ2aWV3cG9ydCIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiaGVhZCIsImxpbmsiLCJyZWwiLCJocmVmIiwibWV0YSIsImNvbnRlbnQiLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/workspace/agentic-demo/web-interface/app/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fblockbase%2Fworkspace%2Fagentic-demo%2Fweb-interface&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();