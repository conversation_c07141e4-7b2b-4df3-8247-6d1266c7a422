import asyncio
import websockets
import json

async def send_new_email():
    """Simulate sending a new email to the email MCP server."""
    async with websockets.connect("ws://localhost:8767") as websocket:
        await websocket.send(json.dumps({
            "type": "new_email",
            "subject": "Urgent: Team Meeting",
            "body": "Please attend the team meeting at 2 PM."
        }))
        print("Sent new email")

if __name__ == "__main__":
    asyncio.run(send_new_email())