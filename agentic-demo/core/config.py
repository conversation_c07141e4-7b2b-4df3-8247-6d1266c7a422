"""Configuration management for the agentic AI system."""

import yaml
import os
from typing import Dict, Any, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class ConfigManager:
    """Manages configuration loading and access."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config.yaml"
        self._config: Dict[str, Any] = {}
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from YAML file."""
        try:
            config_file = Path(self.config_path)
            if not config_file.exists():
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            with open(config_file, 'r') as f:
                self._config = yaml.safe_load(f)
            
            # Override with environment variables if they exist
            self._apply_env_overrides()
            
            logger.info(f"Configuration loaded from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
    
    def _apply_env_overrides(self) -> None:
        """Apply environment variable overrides."""
        # LLM configuration
        if os.getenv("LLM_PROVIDER"):
            self._config["llm"]["provider"] = os.getenv("LLM_PROVIDER")
        if os.getenv("LLM_MODEL"):
            self._config["llm"]["model"] = os.getenv("LLM_MODEL")
        if os.getenv("LLM_TEMPERATURE"):
            self._config["llm"]["temperature"] = float(os.getenv("LLM_TEMPERATURE"))
        
        # WebSocket configuration
        if os.getenv("WEBSOCKET_HOST"):
            self._config["websocket"]["host"] = os.getenv("WEBSOCKET_HOST")
        if os.getenv("USER_INTERFACE_PORT"):
            self._config["websocket"]["user_interface_port"] = int(os.getenv("USER_INTERFACE_PORT"))
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation."""
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_mcp_servers(self) -> Dict[str, Dict[str, Any]]:
        """Get MCP server configurations."""
        return self.get("mcp_servers", {})
    
    def get_agents(self) -> Dict[str, Dict[str, Any]]:
        """Get agent configurations."""
        return self.get("agents", {})
    
    def get_llm_config(self) -> Dict[str, Any]:
        """Get LLM configuration."""
        return self.get("llm", {})
    
    def get_websocket_config(self) -> Dict[str, Any]:
        """Get WebSocket configuration."""
        return self.get("websocket", {})
    
    def get_autonomous_config(self) -> Dict[str, Any]:
        """Get autonomous behavior configuration."""
        return self.get("autonomous", {})
    
    def is_server_enabled(self, server_name: str) -> bool:
        """Check if a specific MCP server is enabled."""
        return self.get(f"mcp_servers.{server_name}.enabled", False)
    
    def is_agent_enabled(self, agent_name: str) -> bool:
        """Check if a specific agent is enabled."""
        return self.get(f"agents.{agent_name}.enabled", False)
    
    def get_server_port(self, server_name: str) -> int:
        """Get WebSocket port for a specific server."""
        return self.get(f"mcp_servers.{server_name}.websocket_port", 8000)
    
    def update(self, key: str, value: Any) -> None:
        """Update configuration value using dot notation."""
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self, path: Optional[str] = None) -> None:
        """Save current configuration to file."""
        save_path = path or self.config_path
        try:
            with open(save_path, 'w') as f:
                yaml.dump(self._config, f, default_flow_style=False, indent=2)
            logger.info(f"Configuration saved to {save_path}")
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            raise

# Global configuration instance
config = ConfigManager()
