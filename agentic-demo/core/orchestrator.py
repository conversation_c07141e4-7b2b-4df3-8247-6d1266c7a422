"""Main orchestrator for the agentic AI system."""

import asyncio
import signal
import logging
from typing import Dict, Any
import websockets
import json

from core.factory import SystemFactory
from core.config import config
from core.base import BaseMCPServer, BaseAgent

logger = logging.getLogger(__name__)

class AgenticOrchestrator:
    """Main orchestrator for the agentic AI system."""
    
    def __init__(self):
        self.servers: Dict[str, BaseMCPServer] = {}
        self.agents: Dict[str, BaseAgent] = {}
        self.user_interface_server = None
        self.is_running = False
        self._shutdown_event = asyncio.Event()
    
    async def initialize(self) -> None:
        """Initialize the orchestrator."""
        logger.info("Initializing Agentic AI Orchestrator...")
        
        # Create the system
        system = await SystemFactory.create_system()
        self.servers = system["servers"]
        self.agents = system["agents"]
        
        # Setup signal handlers for graceful shutdown
        self._setup_signal_handlers()
        
        logger.info("Orchestrator initialized successfully")
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def start(self) -> None:
        """Start the entire agentic AI system."""
        try:
            logger.info("Starting Agentic AI System...")
            
            # Start all MCP servers
            await self._start_servers()
            
            # Start all agents
            await self._start_agents()
            
            # Start user interface WebSocket server
            await self._start_user_interface()
            
            # Start agent WebSocket listeners
            await self._start_agent_listeners()
            
            self.is_running = True
            logger.info("🚀 Agentic AI System started successfully!")
            
            # Wait for shutdown signal
            await self._shutdown_event.wait()
            
        except Exception as e:
            logger.error(f"Failed to start system: {e}")
            raise
    
    async def _start_servers(self) -> None:
        """Start all MCP servers."""
        logger.info("Starting MCP servers...")
        
        # Start servers sequentially to avoid port conflicts
        for server_name, server in self.servers.items():
            try:
                await server.start()
                # Start WebSocket server for this MCP server
                await server.start_websocket_server()
                logger.info(f"✅ Started {server_name}")
            except Exception as e:
                logger.error(f"❌ Failed to start {server_name}: {e}")
                raise
        
        # Small delay to ensure all servers are ready
        await asyncio.sleep(1)
    
    async def _start_agents(self) -> None:
        """Start all agents."""
        logger.info("Starting agents...")
        
        for agent_name, agent in self.agents.items():
            try:
                await agent.start()
                logger.info(f"✅ Started agent {agent_name}")
            except Exception as e:
                logger.error(f"❌ Failed to start agent {agent_name}: {e}")
                raise
    
    async def _start_user_interface(self) -> None:
        """Start the user interface WebSocket server."""
        logger.info("Starting user interface WebSocket server...")
        
        websocket_config = config.get_websocket_config()
        host = websocket_config.get("host", "localhost")
        port = websocket_config.get("user_interface_port", 8765)
        
        try:
            self.user_interface_server = await websockets.serve(
                self._user_interface_handler,
                host,
                port
            )
            logger.info(f"✅ User interface server started on {host}:{port}")
        except Exception as e:
            logger.error(f"❌ Failed to start user interface server: {e}")
            raise
    
    async def _user_interface_handler(self, websocket: websockets.WebSocketServerProtocol, path: str) -> None:
        """Handle user interface WebSocket connections."""
        # Add client to all agents
        for agent in self.agents.values():
            agent.clients.add(websocket)
        
        logger.info("User connected to interface")
        
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self._handle_user_request(data, websocket)
                except json.JSONDecodeError:
                    await websocket.send(json.dumps({
                        "type": "error",
                        "message": "Invalid JSON format"
                    }))
                except Exception as e:
                    logger.error(f"Error handling user request: {e}")
                    await websocket.send(json.dumps({
                        "type": "error",
                        "message": str(e)
                    }))
        except websockets.exceptions.ConnectionClosed:
            logger.info("User disconnected from interface")
        finally:
            # Remove client from all agents
            for agent in self.agents.values():
                agent.clients.discard(websocket)
    
    async def _handle_user_request(self, data: Dict[str, Any], websocket: websockets.WebSocketServerProtocol) -> None:
        """Handle user requests."""
        request_type = data.get("type")
        
        if request_type == "generate_stats_report":
            # Find the proactive agent and generate report
            for agent in self.agents.values():
                if hasattr(agent, 'generate_stats_report'):
                    report = await agent.generate_stats_report()
                    await websocket.send(json.dumps({
                        "type": "report",
                        "data": report
                    }))
                    break
        elif request_type == "list_tasks":
            # Get tasks from task server
            task_server = self.servers.get("task_server")
            if task_server:
                tasks = await task_server.list_tasks()
                await websocket.send(json.dumps({
                    "type": "tasks",
                    "data": tasks
                }))
        elif request_type == "list_emails":
            # Get emails from email server
            email_server = self.servers.get("email_server")
            if email_server:
                emails = await email_server.list_emails()
                await websocket.send(json.dumps({
                    "type": "emails",
                    "data": emails
                }))
        elif request_type == "get_stats":
            # Get stats from biz stats server
            stats_server = self.servers.get("biz_stats_server")
            if stats_server:
                stats = await stats_server.get_stats()
                await websocket.send(json.dumps({
                    "type": "stats",
                    "data": stats
                }))
        else:
            await websocket.send(json.dumps({
                "type": "error",
                "message": f"Unknown request type: {request_type}"
            }))
    
    async def _start_agent_listeners(self) -> None:
        """Start WebSocket listeners for agents."""
        logger.info("Starting agent WebSocket listeners...")
        
        for agent in self.agents.values():
            if hasattr(agent, 'start_websocket_listeners'):
                await agent.start_websocket_listeners()
    
    async def shutdown(self) -> None:
        """Gracefully shutdown the system."""
        if not self.is_running:
            return
        
        logger.info("Shutting down Agentic AI System...")
        self.is_running = False
        
        # Stop agents
        for agent_name, agent in self.agents.items():
            try:
                await agent.stop()
                logger.info(f"✅ Stopped agent {agent_name}")
            except Exception as e:
                logger.error(f"❌ Error stopping agent {agent_name}: {e}")
        
        # Stop servers
        for server_name, server in self.servers.items():
            try:
                await server.stop()
                logger.info(f"✅ Stopped server {server_name}")
            except Exception as e:
                logger.error(f"❌ Error stopping server {server_name}: {e}")
        
        # Stop user interface server
        if self.user_interface_server:
            self.user_interface_server.close()
            await self.user_interface_server.wait_closed()
            logger.info("✅ Stopped user interface server")
        
        logger.info("🛑 Agentic AI System shutdown complete")
        self._shutdown_event.set()

async def main():
    """Main entry point."""
    # Setup logging
    logging.basicConfig(
        level=getattr(logging, config.get("app.log_level", "INFO")),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    orchestrator = AgenticOrchestrator()
    
    try:
        await orchestrator.initialize()
        await orchestrator.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"System error: {e}")
    finally:
        await orchestrator.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
