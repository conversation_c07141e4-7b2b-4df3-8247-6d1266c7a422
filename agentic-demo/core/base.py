"""Base classes for the agentic AI system."""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Set
import asyncio
import websockets
import json
import logging
from beeai_framework.tools import Tool

logger = logging.getLogger(__name__)

class BaseMCPServer(ABC):
    """Abstract base class for MCP servers."""

    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.tools: List[Tool] = []
        self.clients: Set[websockets.WebSocketServerProtocol] = set()
        self.websocket_port = config.get("websocket_port", 8000)
        self.is_running = False
        self._server = None

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the MCP server."""
        pass

    @abstractmethod
    async def create_tools(self) -> List[Tool]:
        """Create and return the tools for this server."""
        pass

    async def start(self) -> None:
        """Start the MCP server."""
        try:
            await self.initialize()
            self.tools = await self.create_tools()
            logger.info(f"{self.name} initialized with {len(self.tools)} tools")
            self.is_running = True
        except Exception as e:
            logger.error(f"Failed to start {self.name}: {e}")
            raise

    async def stop(self) -> None:
        """Stop the MCP server."""
        self.is_running = False
        if self._server:
            self._server.close()
            await self._server.wait_closed()
        logger.info(f"{self.name} stopped")

    async def get_tools(self) -> List[Tool]:
        """Get the tools for this server."""
        return self.tools

    async def notify_clients(self, message_type: str, data: Any) -> None:
        """Notify all connected WebSocket clients."""
        if not self.clients:
            return

        message = json.dumps({"type": message_type, "data": data})
        disconnected_clients = set()

        for client in self.clients:
            try:
                await client.send(message)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                logger.warning(f"Failed to send message to client: {e}")
                disconnected_clients.add(client)

        # Remove disconnected clients
        self.clients -= disconnected_clients

    async def websocket_handler(self, websocket: websockets.WebSocketServerProtocol, path: str) -> None:
        """Handle WebSocket connections."""
        self.clients.add(websocket)
        logger.info(f"Client connected to {self.name} WebSocket server")

        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_websocket_message(data, websocket)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON received: {message}")
                except Exception as e:
                    logger.error(f"Error handling WebSocket message: {e}")
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client disconnected from {self.name} WebSocket server")
        finally:
            self.clients.discard(websocket)

    @abstractmethod
    async def handle_websocket_message(self, data: Dict[str, Any], websocket: websockets.WebSocketServerProtocol) -> None:
        """Handle incoming WebSocket messages."""
        pass

    async def start_websocket_server(self) -> None:
        """Start the WebSocket server."""
        try:
            # Create a wrapper function that matches the expected signature
            async def handler_wrapper(websocket, path):
                return await self.websocket_handler(websocket, path)

            self._server = await websockets.serve(
                handler_wrapper,
                "localhost",
                self.websocket_port
            )
            logger.info(f"{self.name} WebSocket server started on port {self.websocket_port}")
        except Exception as e:
            logger.error(f"Failed to start WebSocket server for {self.name}: {e}")
            raise

class BaseAgent(ABC):
    """Abstract base class for agents."""

    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.mcp_servers: Dict[str, BaseMCPServer] = {}
        self.clients: Set[websockets.WebSocketServerProtocol] = set()
        self.is_running = False
        self._tasks: List[asyncio.Task] = []

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the agent."""
        pass

    @abstractmethod
    async def create_llm_agent(self) -> Any:
        """Create the underlying LLM agent."""
        pass

    async def add_mcp_server(self, server: BaseMCPServer) -> None:
        """Add an MCP server to this agent."""
        self.mcp_servers[server.name] = server
        logger.info(f"Added MCP server {server.name} to agent {self.name}")

    async def start(self) -> None:
        """Start the agent."""
        try:
            await self.initialize()
            await self.create_llm_agent()
            self.is_running = True
            logger.info(f"Agent {self.name} started successfully")
        except Exception as e:
            logger.error(f"Failed to start agent {self.name}: {e}")
            raise

    async def stop(self) -> None:
        """Stop the agent."""
        self.is_running = False

        # Cancel all running tasks
        for task in self._tasks:
            if not task.done():
                task.cancel()

        # Wait for tasks to complete
        if self._tasks:
            await asyncio.gather(*self._tasks, return_exceptions=True)

        logger.info(f"Agent {self.name} stopped")

    @abstractmethod
    async def handle_notification(self, notification_type: str, data: Any) -> None:
        """Handle notifications from MCP servers."""
        pass

    async def notify_users(self, message: str) -> None:
        """Send a notification to all connected user clients."""
        if not self.clients:
            return

        notification = json.dumps({"type": "notification", "message": message})
        disconnected_clients = set()

        for client in self.clients:
            try:
                await client.send(notification)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                logger.warning(f"Failed to send notification to user: {e}")
                disconnected_clients.add(client)

        # Remove disconnected clients
        self.clients -= disconnected_clients

class BaseToolFactory(ABC):
    """Abstract base class for tool factories."""

    @abstractmethod
    async def create_tool(self, tool_config: Dict[str, Any]) -> Tool:
        """Create a tool from configuration."""
        pass
