"""Factory classes for creating servers and agents."""

import importlib
from typing import Dict, Any, List
import logging

from core.base import BaseMCPServer, BaseAgent
from core.config import config

logger = logging.getLogger(__name__)

class MCPServerFactory:
    """Factory for creating MCP servers."""
    
    @staticmethod
    async def create_server(server_name: str, server_config: Dict[str, Any]) -> BaseMCPServer:
        """Create an MCP server from configuration."""
        try:
            module_name = server_config["module"]
            class_name = server_config["class"]
            
            # Import the module
            module = importlib.import_module(module_name)
            server_class = getattr(module, class_name)
            
            # Create the server instance
            server = server_class(server_name, server_config)
            
            logger.info(f"Created MCP server: {server_name}")
            return server
            
        except Exception as e:
            logger.error(f"Failed to create MCP server {server_name}: {e}")
            raise

    @staticmethod
    async def create_all_servers() -> Dict[str, BaseMCPServer]:
        """Create all enabled MCP servers from configuration."""
        servers = {}
        server_configs = config.get_mcp_servers()
        
        for server_name, server_config in server_configs.items():
            if server_config.get("enabled", False):
                try:
                    server = await MCPServerFactory.create_server(server_name, server_config)
                    servers[server_name] = server
                except Exception as e:
                    logger.error(f"Failed to create server {server_name}: {e}")
                    # Continue with other servers
        
        logger.info(f"Created {len(servers)} MCP servers")
        return servers

class AgentFactory:
    """Factory for creating agents."""
    
    @staticmethod
    async def create_agent(agent_name: str, agent_config: Dict[str, Any]) -> BaseAgent:
        """Create an agent from configuration."""
        try:
            module_name = agent_config["module"]
            class_name = agent_config["class"]
            
            # Import the module
            module = importlib.import_module(module_name)
            agent_class = getattr(module, class_name)
            
            # Create the agent instance
            agent = agent_class(agent_name, agent_config)
            
            logger.info(f"Created agent: {agent_name}")
            return agent
            
        except Exception as e:
            logger.error(f"Failed to create agent {agent_name}: {e}")
            raise

    @staticmethod
    async def create_all_agents() -> Dict[str, BaseAgent]:
        """Create all enabled agents from configuration."""
        agents = {}
        agent_configs = config.get_agents()
        
        for agent_name, agent_config in agent_configs.items():
            if agent_config.get("enabled", False):
                try:
                    agent = await AgentFactory.create_agent(agent_name, agent_config)
                    agents[agent_name] = agent
                except Exception as e:
                    logger.error(f"Failed to create agent {agent_name}: {e}")
                    # Continue with other agents
        
        logger.info(f"Created {len(agents)} agents")
        return agents

class SystemFactory:
    """Factory for creating the entire system."""
    
    @staticmethod
    async def create_system() -> Dict[str, Any]:
        """Create the complete agentic AI system."""
        logger.info("Creating agentic AI system...")
        
        # Create MCP servers
        servers = await MCPServerFactory.create_all_servers()
        
        # Create agents
        agents = await AgentFactory.create_all_agents()
        
        # Connect agents to servers
        for agent in agents.values():
            for server in servers.values():
                await agent.add_mcp_server(server)
        
        system = {
            "servers": servers,
            "agents": agents
        }
        
        logger.info(f"System created with {len(servers)} servers and {len(agents)} agents")
        return system
