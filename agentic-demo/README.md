# Autonomous Agentic AI System

A fully autonomous AI system built with BeeAI framework and WebSocket communication for real-time interaction with MCP (Model Context Protocol) servers.

## 🚀 Features

- **Fully Autonomous Behavior**: AI agent monitors data changes and proactively provides assistance
- **Scalable Architecture**: Easy to add new agents, MCP servers, and tools
- **Real-time Communication**: WebSocket-based communication for instant notifications
- **Configuration-Driven**: YAML configuration for easy customization
- **Best Practices**: Proper error handling, logging, and graceful shutdown

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Client   │    │  Proactive AI   │    │  MCP Servers    │
│                 │    │     Agent       │    │                 │
│ - Demo Client   │◄──►│                 │◄──►│ - Task Server   │
│ - Web Interface │    │ - <PERSON><PERSON><PERSON> LLM     │    │ - Email Server  │
│ - Custom Apps   │    │ - Autonomous    │    │ - Stats Server  │
└─────────────────┘    │   Triggers      │    └─────────────────┘
                       └─────────────────┘
```

## 📋 Components

### MCP Servers
- **Task Server** (Port 8766): Manages tasks and assignments
- **Email Server** (Port 8767): Handles email notifications
- **Business Stats Server** (Port 8768): Tracks business metrics

### AI Agent
- **Proactive Agent**: Monitors all servers and provides autonomous assistance
- **Autonomous Triggers**: Responds to new tasks, emails, and revenue changes
- **Report Generation**: Creates AI-powered business analysis

### User Interface
- **WebSocket Server** (Port 8765): Main interface for user interaction
- **Demo Client**: Interactive demonstration of capabilities

## 🛠️ Installation

1. **Install Dependencies**:
   ```bash
   pip3 install -r requirements.txt
   ```

2. **Configure the System** (optional):
   Edit `config.yaml` to customize:
   - LLM settings (model, temperature)
   - Server ports
   - Autonomous behavior triggers
   - Agent configurations

## 🎮 Usage

### Start the System
```bash
python3 main.py
```

You should see:
```
🤖 Starting Autonomous Agentic AI System...
📋 Loading configuration and initializing components...
🔗 Setting up WebSocket communication channels...
🧠 Creating AI agents with autonomous behavior...

✅ Started task_server
✅ Started email_server  
✅ Started biz_stats_server
✅ Started agent main_agent
✅ User interface server started on localhost:8765
🚀 Agentic AI System started successfully!
```

### Run the Demo
In another terminal:
```bash
python3 demo_client.py
```

The demo will:
1. Connect to the AI system
2. Show current data from all servers
3. Generate an AI business report
4. Add new data to trigger autonomous behavior
5. Display real-time AI notifications

### Stop the System
Press `Ctrl+C` in the main terminal. The system will gracefully shutdown all components.

## 🤖 Autonomous Behavior

The AI agent automatically:

- **New Task Notifications**: Alerts when tasks are assigned to you
- **Email Summaries**: Offers to summarize new emails
- **Revenue Analysis**: Detects revenue declines and offers analysis
- **Proactive Reports**: Generates business insights on demand

## 🔧 Configuration

### Adding New MCP Servers

1. **Create Server Class**:
   ```python
   # servers/my_server.py
   from core.base import BaseMCPServer
   
   class MyMCPServer(BaseMCPServer):
       # Implementation
   ```

2. **Add to Configuration**:
   ```yaml
   # config.yaml
   mcp_servers:
     my_server:
       name: "MyMCPServer"
       module: "servers.my_server"
       class: "MyMCPServer"
       websocket_port: 8769
       enabled: true
   ```

### Adding New Agents

1. **Create Agent Class**:
   ```python
   # agents/my_agent.py
   from core.base import BaseAgent
   
   class MyAgent(BaseAgent):
       # Implementation
   ```

2. **Add to Configuration**:
   ```yaml
   # config.yaml
   agents:
     my_agent:
       name: "MyAgent"
       module: "agents.my_agent"
       class: "MyAgent"
       enabled: true
   ```

### Customizing Autonomous Triggers

```yaml
# config.yaml
agents:
  main_agent:
    config:
      autonomous_triggers:
        - type: "new_task"
          condition: "assignee == 'user'"
          action: "notify_user"
        - type: "revenue_decline"
          condition: "revenue < previous_revenue * 0.95"
          action: "generate_alert"
```

## 📊 WebSocket API

### User Interface (Port 8765)

**Requests**:
```json
{"type": "list_tasks"}
{"type": "list_emails"}
{"type": "get_stats"}
{"type": "generate_stats_report"}
```

**Responses**:
```json
{"type": "notification", "message": "AI notification"}
{"type": "tasks", "data": [...]}
{"type": "report", "data": "AI-generated report"}
```

### MCP Servers

**Task Server (Port 8766)**:
```json
{"type": "new_task", "description": "...", "assignee": "..."}
{"type": "list_tasks"}
```

**Email Server (Port 8767)**:
```json
{"type": "new_email", "subject": "...", "body": "..."}
{"type": "send_email", "to": "...", "subject": "...", "body": "..."}
```

**Stats Server (Port 8768)**:
```json
{"type": "new_stats", "month": "2025-01", "revenue": 100000, "sales_volume": 500}
{"type": "get_stats"}
```

## 🧪 Testing

Run the test suite:
```bash
python3 test_system.py
```

## 📝 Logs

The system provides comprehensive logging:
- Server startup/shutdown
- WebSocket connections
- AI agent decisions
- Error handling

Log level can be configured in `config.yaml`:
```yaml
app:
  log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR
```

## 🔒 Security Notes

- Currently configured for localhost only
- No authentication implemented (suitable for development)
- For production use, add proper authentication and encryption

## 🤝 Contributing

1. Follow the existing architecture patterns
2. Add proper error handling and logging
3. Update configuration schema as needed
4. Test with the demo client

## 📄 License

This project demonstrates autonomous agentic AI architecture and is provided as-is for educational and development purposes.
