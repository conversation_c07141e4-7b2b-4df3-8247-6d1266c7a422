from beeai_framework.agents.react.agent import ReActAgent
from beeai_framework.backend.chat import <PERSON><PERSON><PERSON><PERSON><PERSON>, ChatModelParameters
from beeai_framework.memory.token_memory import TokenMemory
import websockets
import json
import asyncio
import statistics

class ProactiveAgent:
    """Proactive AI agent that monitors tasks, emails, and business stats via WebSocket and handles user requests."""
    def __init__(self, task_mcp, email_mcp, biz_stats_mcp):
        self.task_mcp = task_mcp
        self.email_mcp = email_mcp
        self.biz_stats_mcp = biz_stats_mcp
        self.seen_tasks = set()
        self.seen_emails = set()
        self.seen_stats = set()
        self.clients = set()
        self.agent = None

    async def create(self):
        """Create the ReActAgent with MCP tools."""
        llm = ChatModel.from_name("ollama:llama3.1", ChatModelParameters(temperature=0))
        task_tools = await self.task_mcp.get_tools()
        email_tools = await self.email_mcp.get_tools()
        stats_tools = await self.biz_stats_mcp.get_tools()
        tools = task_tools + email_tools + stats_tools
        self.agent = ReActAgent(
            llm=llm,
            tools=tools,
            memory=TokenMemory(llm)
        )

    async def notify(self, message):
        """Send a notification to all connected WebSocket clients."""
        if self.clients:
            await asyncio.gather(*[client.send(json.dumps({"type": "notification", "message": message})) for client in self.clients])

    async def handle_task_update(self, task):
        """Handle new task updates from WebSocket."""
        if task["assignee"] == "user" and task["task_id"] not in self.seen_tasks:
            self.seen_tasks.add(task["task_id"])
            await self.notify(f"New task assigned: {task['description']}")

    async def handle_email_update(self, email):
        """Handle new email updates from WebSocket."""
        if email["email_id"] not in self.seen_emails:
            self.seen_emails.add(email["email_id"])
            await self.notify(f"New email received: {email['subject']}. Would you like a summary? (yes/no)")

    async def handle_stats_update(self, stats):
        """Handle new business stats updates from WebSocket."""
        if stats["month"] not in self.seen_stats:
            self.seen_stats.add(stats["month"])
            await self.notify(f"New business stats received for {stats['month']}. Would you like an analysis report? (yes/no)")

    async def handle_user_request(self, request):
        """Handle user requests from WebSocket."""
        if request.get("type") == "response":
            request_type = request.get("request_type")
            response = request.get("response", "").lower()
            if request_type == "email_summary" and response == "yes":
                email_id = request.get("email_id")
                emails = await self.email_mcp.list_emails()
                email = next((e for e in emails if e["email_id"] == email_id), None)
                if email:
                    summary = await self.summarize_email(email)
                    await self.notify(f"Summary: {summary}")
                    await self.notify(f"Would you like to draft a response to '{email['subject']}'? (yes/no)")
            elif request_type == "email_draft" and response == "yes":
                email_id = request.get("email_id")
                emails = await self.email_mcp.list_emails()
                email = next((e for e in emails if e["email_id"] == email_id), None)
                if email:
                    draft = await self.draft_email_response(email)
                    await self.notify(f"Draft response: {draft}")
                    await self.email_mcp.send_email("<EMAIL>", f"Re: {email['subject']}", draft)
            elif request_type == "stats_report" and response == "yes":
                report = await self.generate_stats_report()
                await self.notify(f"Analysis Report:\n{report}")
            elif request_type == "generate_stats_report":
                report = await self.generate_stats_report()
                await self.notify(f"Analysis Report:\n{report}")

    async def summarize_email(self, email):
        """Summarize an email using the LLM."""
        prompt = f"Summarize the following email in 50 words or less:\nSubject: {email['subject']}\nBody: {email['body']}"
        response = await self.agent.run(prompt)
        return response.get("final_answer", "Summary failed.")

    async def draft_email_response(self, email):
        """Draft an email response using the LLM."""
        prompt = f"Draft a professional response to the following email:\nSubject: {email['subject']}\nBody: {email['body']}"
        response = await self.agent.run(prompt)
        return response.get("final_answer", "Draft failed.")

    async def generate_stats_report(self):
        """Generate a report analyzing business stats with forecast and revenue decline reasons."""
        stats = await self.biz_stats_mcp.get_stats()
        revenues = [s["revenue"] for s in stats]
        if len(revenues) >= 3:
            forecast = statistics.mean(revenues[-3:])  # 3-month moving average
        else:
            forecast = revenues[-1] if revenues else 0
        
        decline_reasons = []
        for i in range(1, len(revenues)):
            if revenues[i] < revenues[i-1]:
                decline_reasons.append(f"Decline in {stats[i]['month']}: Revenue dropped from {revenues[i-1]} to {revenues[i]}.")
        
        prompt = f"""
        Analyze the following business statistics:
        {json.dumps(stats, indent=2)}
        Provide a report with:
        1. Forecast for next month's revenue based on a 3-month moving average ({forecast}).
        2. Reasons for any revenue declines.
        3. Recommendations to improve revenue.
        """
        response = await self.agent.run(prompt)
        report = response.get("final_answer", "Report generation failed.")
        
        return f"""
            <xaiArtifact artifact_id="8f63a446-08bc-4ff6-b257-71ba854f14fa" artifact_version_id="65b0ffb1-b16b-4013-a60b-e09dc6a4f9f7" title="Business_Stats_Report.md" contentType="text/markdown">
            # Business Statistics Report

            ## Revenue Forecast
            Next month's revenue forecast (3-month moving average): ${forecast:.2f}

            ## Revenue Decline Analysis
            {chr(10).join(decline_reasons) or "No significant declines detected."}

            ## LLM Analysis and Recommendations
            {report}
            </xaiArtifact>
        """
    
    async def task_websocket_listener(self):
        """Listen for task updates via WebSocket."""
        async with websockets.connect("ws://localhost:8766") as websocket:
            await websocket.send(json.dumps({"type": "subscribe"}))
            async for message in websocket:
                data = json.loads(message)
                if data["type"] == "task":
                    await self.handle_task_update(data["data"])

    async def email_websocket_listener(self):
        """Listen for email updates via WebSocket."""
        async with websockets.connect("ws://localhost:8767") as websocket:
            await websocket.send(json.dumps({"type": "subscribe"}))
            async for message in websocket:
                data = json.loads(message)
                if data["type"] == "email":
                    await self.handle_email_update(data["data"])

    async def stats_websocket_listener(self):
        """Listen for business stats updates via WebSocket."""
        async with websockets.connect("ws://localhost:8768") as websocket:
            await websocket.send(json.dumps({"type": "subscribe"}))
            async for message in websocket:
                data = json.loads(message)
                if data["type"] == "stats":
                    await self.handle_stats_update(data["data"])

    async def create_agent(task_mcp, email_mcp, biz_stats_mcp):
        """Create and configure the proactive agent with WebSocket listeners."""
        agent = ProactiveAgent(task_mcp, email_mcp, biz_stats_mcp)
        await agent.create()
        asyncio.create_task(agent.task_websocket_listener())
        asyncio.create_task(agent.email_websocket_listener())
        asyncio.create_task(agent.stats_websocket_listener())
        return agent