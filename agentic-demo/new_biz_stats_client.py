import asyncio
import websockets
import json

async def send_new_stats():
    """Simulate sending new business stats to the biz stats MCP server."""
    async with websockets.connect("ws://localhost:8768") as websocket:
        await websocket.send(json.dumps({
            "type": "new_stats",
            "month": "2025-04",
            "revenue": 90000,
            "sales_volume": 450
        }))
        print("Sent new business stats")

if __name__ == "__main__":
    asyncio.run(send_new_stats())