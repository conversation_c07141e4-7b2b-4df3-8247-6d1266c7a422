"""Business statistics MCP server."""

import json
from typing import Dict, Any, List
import websockets
import logging
from beeai_framework.tools import Tool
from beeai_framework.emitter import Emitter

from core.base import BaseMCPServer

logger = logging.getLogger(__name__)

class GetStatsTool(Tool):
    """Tool for getting business statistics."""
    
    def __init__(self, stats_server):
        self.stats_server = stats_server
        super().__init__()
    
    @property
    def name(self) -> str:
        return "get_stats"
    
    @property
    def description(self) -> str:
        return "Get business statistics"
    
    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {},
            "required": []
        }
    
    def _create_emitter(self) -> Emitter:
        return Emitter()
    
    async def _run(self, **kwargs) -> Dict[str, Any]:
        """Execute the get stats tool."""
        stats = await self.stats_server.get_stats()
        return {"stats": stats}

class AddStatsTool(Tool):
    """Tool for adding new business statistics."""
    
    def __init__(self, stats_server):
        self.stats_server = stats_server
        super().__init__()
    
    @property
    def name(self) -> str:
        return "add_stats"
    
    @property
    def description(self) -> str:
        return "Add new business statistics"
    
    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "month": {"type": "string", "description": "Month in YYYY-MM format"},
                "revenue": {"type": "number", "description": "Revenue amount"},
                "sales_volume": {"type": "number", "description": "Sales volume"}
            },
            "required": ["month", "revenue", "sales_volume"]
        }
    
    def _create_emitter(self) -> Emitter:
        return Emitter()
    
    async def _run(self, month: str, revenue: float, sales_volume: int, **kwargs) -> Dict[str, Any]:
        """Execute the add stats tool."""
        stats = await self.stats_server.add_stats(month, revenue, sales_volume)
        return {"stats": stats, "status": "added"}

class BizStatsMCPServer(BaseMCPServer):
    """Business statistics server with WebSocket push updates."""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.stats = []
    
    async def initialize(self) -> None:
        """Initialize the stats server."""
        # Load initial stats from config
        initial_stats = self.config.get("config", {}).get("initial_stats", [])
        self.stats = initial_stats.copy()
        logger.info(f"Initialized {self.name} with {len(self.stats)} initial stats")
    
    async def create_tools(self) -> List[Tool]:
        """Create and return the tools for this server."""
        return [
            GetStatsTool(self),
            AddStatsTool(self)
        ]
    
    async def get_stats(self) -> List[Dict[str, Any]]:
        """Get business statistics."""
        return self.stats
    
    async def add_stats(self, month: str, revenue: float, sales_volume: int) -> Dict[str, Any]:
        """Add new business stats and notify clients."""
        new_stats = {"month": month, "revenue": revenue, "sales_volume": sales_volume}
        self.stats.append(new_stats)
        
        # Notify clients
        await self.notify_clients("stats", new_stats)
        
        logger.info(f"Added new stats: {new_stats}")
        return new_stats
    
    async def handle_websocket_message(self, data: Dict[str, Any], websocket: websockets.WebSocketServerProtocol) -> None:
        """Handle incoming WebSocket messages."""
        message_type = data.get("type")
        
        if message_type == "new_stats":
            month = data.get("month")
            revenue = data.get("revenue")
            sales_volume = data.get("sales_volume")
            if month and revenue is not None and sales_volume is not None:
                await self.add_stats(month, revenue, sales_volume)
            else:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "Missing month, revenue, or sales_volume"
                }))
        elif message_type == "get_stats":
            stats = await self.get_stats()
            await websocket.send(json.dumps({
                "type": "stats",
                "data": stats
            }))
        else:
            logger.warning(f"Unknown message type: {message_type}")
