"""Email management MCP server."""

import json
from typing import Dict, Any, List
import websockets
import logging
from beeai_framework.tools import Tool
from beeai_framework.emitter import Emitter

from core.base import BaseMCPServer

logger = logging.getLogger(__name__)

class EmailListTool(Tool):
    """Tool for listing emails."""
    
    def __init__(self, email_server):
        self.email_server = email_server
        super().__init__()
    
    @property
    def name(self) -> str:
        return "list_emails"
    
    @property
    def description(self) -> str:
        return "List all emails"
    
    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {},
            "required": []
        }
    
    def _create_emitter(self) -> Emitter:
        return Emitter()
    
    async def _run(self, **kwargs) -> Dict[str, Any]:
        """Execute the email listing tool."""
        emails = await self.email_server.list_emails()
        return {"emails": emails}

class SendEmailTool(Tool):
    """Tool for sending emails."""
    
    def __init__(self, email_server):
        self.email_server = email_server
        super().__init__()
    
    @property
    def name(self) -> str:
        return "send_email"
    
    @property
    def description(self) -> str:
        return "Send an email"
    
    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "to": {"type": "string", "description": "Recipient email address"},
                "subject": {"type": "string", "description": "Email subject"},
                "body": {"type": "string", "description": "Email body"}
            },
            "required": ["to", "subject", "body"]
        }
    
    def _create_emitter(self) -> Emitter:
        return Emitter()
    
    async def _run(self, to: str, subject: str, body: str, **kwargs) -> Dict[str, Any]:
        """Execute the send email tool."""
        result = await self.email_server.send_email(to, subject, body)
        return result

class EmailMCPServer(BaseMCPServer):
    """Email management server with WebSocket push updates."""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.emails = []
    
    async def initialize(self) -> None:
        """Initialize the email server."""
        # Load initial emails from config
        initial_emails = self.config.get("config", {}).get("initial_emails", [])
        self.emails = initial_emails.copy()
        logger.info(f"Initialized {self.name} with {len(self.emails)} initial emails")
    
    async def create_tools(self) -> List[Tool]:
        """Create and return the tools for this server."""
        return [
            EmailListTool(self),
            SendEmailTool(self)
        ]
    
    async def list_emails(self) -> List[Dict[str, Any]]:
        """List all emails."""
        return self.emails
    
    async def send_email(self, to: str, subject: str, body: str) -> Dict[str, Any]:
        """Send an email."""
        result = {"status": "sent", "to": to, "subject": subject, "body": body}
        logger.info(f"Email sent: {result}")
        return result
    
    async def add_email(self, subject: str, body: str) -> Dict[str, Any]:
        """Add a new email and notify clients."""
        email_id = len(self.emails) + 1
        new_email = {"email_id": email_id, "subject": subject, "body": body}
        self.emails.append(new_email)
        
        # Notify clients
        await self.notify_clients("email", new_email)
        
        logger.info(f"Added new email: {new_email}")
        return new_email
    
    async def handle_websocket_message(self, data: Dict[str, Any], websocket: websockets.WebSocketServerProtocol) -> None:
        """Handle incoming WebSocket messages."""
        message_type = data.get("type")
        
        if message_type == "new_email":
            subject = data.get("subject")
            body = data.get("body")
            if subject and body:
                await self.add_email(subject, body)
            else:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "Missing subject or body"
                }))
        elif message_type == "list_emails":
            emails = await self.list_emails()
            await websocket.send(json.dumps({
                "type": "emails",
                "data": emails
            }))
        elif message_type == "send_email":
            to = data.get("to")
            subject = data.get("subject")
            body = data.get("body")
            if to and subject and body:
                result = await self.send_email(to, subject, body)
                await websocket.send(json.dumps({
                    "type": "email_sent",
                    "data": result
                }))
            else:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "Missing to, subject, or body"
                }))
        else:
            logger.warning(f"Unknown message type: {message_type}")
