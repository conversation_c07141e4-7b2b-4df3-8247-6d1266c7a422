"""Task management MCP server."""

import json
from typing import Dict, Any, List
import websockets
import logging
from beeai_framework.tools import Tool
from beeai_framework.emitter import Emitter

from core.base import BaseMCPServer

logger = logging.getLogger(__name__)

class TaskListTool(Tool):
    """Tool for listing tasks."""
    
    def __init__(self, task_server):
        self.task_server = task_server
        super().__init__()
    
    @property
    def name(self) -> str:
        return "list_tasks"
    
    @property
    def description(self) -> str:
        return "List all tasks"
    
    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {},
            "required": []
        }
    
    def _create_emitter(self) -> Emitter:
        return Emitter()
    
    async def _run(self, **kwargs) -> Dict[str, Any]:
        """Execute the task listing tool."""
        tasks = await self.task_server.list_tasks()
        return {"tasks": tasks}

class AddTaskTool(Tool):
    """Tool for adding new tasks."""
    
    def __init__(self, task_server):
        self.task_server = task_server
        super().__init__()
    
    @property
    def name(self) -> str:
        return "add_task"
    
    @property
    def description(self) -> str:
        return "Add a new task"
    
    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "description": {"type": "string", "description": "Task description"},
                "assignee": {"type": "string", "description": "Task assignee"}
            },
            "required": ["description", "assignee"]
        }
    
    def _create_emitter(self) -> Emitter:
        return Emitter()
    
    async def _run(self, description: str, assignee: str, **kwargs) -> Dict[str, Any]:
        """Execute the add task tool."""
        task = await self.task_server.add_task(description, assignee)
        return {"task": task, "status": "added"}

class TaskMCPServer(BaseMCPServer):
    """Task management server with WebSocket push updates."""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        super().__init__(name, config)
        self.tasks = []
    
    async def initialize(self) -> None:
        """Initialize the task server."""
        # Load initial tasks from config
        initial_tasks = self.config.get("config", {}).get("initial_tasks", [])
        self.tasks = initial_tasks.copy()
        logger.info(f"Initialized {self.name} with {len(self.tasks)} initial tasks")
    
    async def create_tools(self) -> List[Tool]:
        """Create and return the tools for this server."""
        return [
            TaskListTool(self),
            AddTaskTool(self)
        ]
    
    async def list_tasks(self) -> List[Dict[str, Any]]:
        """List all tasks."""
        return self.tasks
    
    async def add_task(self, description: str, assignee: str) -> Dict[str, Any]:
        """Add a new task and notify clients."""
        task_id = len(self.tasks) + 1
        new_task = {"task_id": task_id, "description": description, "assignee": assignee}
        self.tasks.append(new_task)
        
        # Notify clients
        await self.notify_clients("task", new_task)
        
        logger.info(f"Added new task: {new_task}")
        return new_task
    
    async def handle_websocket_message(self, data: Dict[str, Any], websocket: websockets.WebSocketServerProtocol) -> None:
        """Handle incoming WebSocket messages."""
        message_type = data.get("type")
        
        if message_type == "new_task":
            description = data.get("description")
            assignee = data.get("assignee")
            if description and assignee:
                await self.add_task(description, assignee)
            else:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "Missing description or assignee"
                }))
        elif message_type == "list_tasks":
            tasks = await self.list_tasks()
            await websocket.send(json.dumps({
                "type": "tasks",
                "data": tasks
            }))
        else:
            logger.warning(f"Unknown message type: {message_type}")
