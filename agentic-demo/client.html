<!DOCTYPE html>
<html>
<head>
    <title><PERSON></title>
    <style>
        #notifications { margin-bottom: 20px; }
        #requestForm { margin-top: 20px; }
        #requestInput { width: 300px; padding: 5px; }
        button { padding: 5px 10px; }
    </style>
</head>
<body>
    <h1>Jarvis Notifications</h1>
    <div id="notifications"></div>
    <form id="requestForm">
        <input type="text" id="requestInput" placeholder="Enter request (e.g., generate stats report)">
        <button type="submit">Send Request</button>
    </form>
    <script>
        const ws = new WebSocket("ws://localhost:8765");
        let currentEmailId = null;
        let currentRequestType = null;

        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            const notifications = document.getElementById("notifications");
            const p = document.createElement("p");
            p.textContent = data.message;
            notifications.appendChild(p);

            // Detect prompts for user input
            if (data.message.includes("Would you like a summary?")) {
                currentRequestType = "email_summary";
                currentEmailId = data.email_id || extractEmailId(data.message);
            } else if (data.message.includes("Would you like to draft a response?")) {
                currentRequestType = "email_draft";
                currentEmailId = data.email_id || extractEmailId(data.message);
            } else if (data.message.includes("Would you like an analysis report?")) {
                currentRequestType = "stats_report";
            }
        };

        function extractEmailId(message) {
            // Placeholder to extract email_id if needed; assume it's sent in real implementation
            return null;
        }

        document.getElementById("requestForm").onsubmit = function(event) {
            event.preventDefault();
            const input = document.getElementById("requestInput").value.trim().toLowerCase();
            let message = {};

            if (input === "generate stats report") {
                message = { type: "response", request_type: "generate_stats_report" };
            } else if (currentRequestType && (input === "yes" || input === "no")) {
                message = {
                    type: "response",
                    request_type: currentRequestType,
                    response: input,
                    email_id: currentEmailId
                };
            } else {
                alert("Invalid request. Try 'generate stats report' or 'yes/no' for prompts.");
                return;
            }

            ws.send(JSON.stringify(message));
            document.getElementById("requestInput").value = "";
        };
    </script>
</body>
</html>