import websockets
import asyncio
import json

async def handler(websocket, path, agent):
    """Handle WebSocket connections for user notifications and input."""
    agent.clients.add(websocket)
    try:
        async for message in websocket:
            data = json.loads(message)
            await agent.handle_user_request(data)
    finally:
        agent.clients.remove(websocket)

async def start_websocket_server(agent):
    """Start the WebSocket server for user notifications and input."""
    async with websockets.serve(lambda ws, path: handler(ws, path, agent), "localhost", 8765):
        await asyncio.Future()  # Run forever