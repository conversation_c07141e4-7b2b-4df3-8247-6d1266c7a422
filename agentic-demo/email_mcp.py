from mcp import StdioServerParameters, ClientSession, stdio_client
from beeai_framework.tools.mcp.mcp import MCPTool
import json
import asyncio
import websockets

class EmailMCPServer:
    """Simulated MCP server for email list with WebSocket push updates."""
    def __init__(self):
        self.emails = [
            {"email_id": 1, "subject": "Meeting Tomorrow", "body": "Hi, let's meet at 10 AM."},
            {"email_id": 2, "subject": "Project Update", "body": "The project is on track."}
        ]
        self.session = None
        self.clients = set()

    async def start(self):
        """Start the simulated MCP server."""
        self.server_params = StdioServerParameters(
            command="echo",
            args=["{\"tools\": [{\"name\": \"list_emails\", \"description\": \"List all emails\", \"input_schema\": {}}, {\"name\": \"send_email\", \"description\": \"Send an email\", \"input_schema\": {\"to\": \"string\", \"subject\": \"string\", \"body\": \"string\"}}]}"]
        )
        async with stdio_client(self.server_params) as (read, write):
            self.session = ClientSession(read, write)
            await self.session.initialize()

    async def stop(self):
        """Stop the MCP server."""
        if self.session:
            await self.session.close()

    async def list_emails(self):
        """Simulate listing emails."""
        return self.emails

    async def send_email(self, to, subject, body):
        """Simulate sending an email."""
        return {"status": "sent", "to": to, "subject": subject, "body": body}

    async def add_email(self, subject, body):
        """Simulate adding a new email and notify clients."""
        email_id = len(self.emails) + 1
        new_email = {"email_id": email_id, "subject": subject, "body": body}
        self.emails.append(new_email)
        await self.notify_clients(new_email)

    async def notify_clients(self, email):
        """Notify all WebSocket clients of a new email."""
        if self.clients:
            await asyncio.gather(*[client.send(json.dumps({"type": "email", "data": email})) for client in self.clients])

    async def websocket_handler(self, websocket, path):
        """Handle WebSocket connections for email updates."""
        self.clients.add(websocket)
        try:
            async for message in websocket:
                data = json.loads(message)
                if data.get("type") == "new_email":
                    await self.add_email(data["subject"], data["body"])
        finally:
            self.clients.remove(websocket)

    async def start_websocket_listener(self, agent):
        """Start the WebSocket server for email updates."""
        async with websockets.serve(lambda ws, path: self.websocket_handler(ws, path), "localhost", 8767):
            await asyncio.Future()  # Run forever

    async def get_tools(self):
        """Get MCP tools for the agent."""
        return await MCPTool.from_client(self.session, self.server_params)