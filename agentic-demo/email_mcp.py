import json
import async<PERSON>
import websockets
from typing import Dict, Any
from beeai_framework.tools import Tool
from beeai_framework.tools.events import ToolEmitter

class EmailListTool(Tool):
    """Tool for listing emails."""

    def __init__(self, email_server):
        self.email_server = email_server
        super().__init__()

    @property
    def name(self) -> str:
        return "list_emails"

    @property
    def description(self) -> str:
        return "List all emails"

    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {},
            "required": []
        }

    def _create_emitter(self) -> ToolEmitter:
        return ToolEmitter()

    async def _run(self, **kwargs) -> Dict[str, Any]:
        """Execute the email listing tool."""
        emails = await self.email_server.list_emails()
        return {"emails": emails}

class SendEmailTool(Tool):
    """Tool for sending emails."""

    def __init__(self, email_server):
        self.email_server = email_server
        super().__init__()

    @property
    def name(self) -> str:
        return "send_email"

    @property
    def description(self) -> str:
        return "Send an email"

    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "to": {"type": "string", "description": "Recipient email address"},
                "subject": {"type": "string", "description": "Email subject"},
                "body": {"type": "string", "description": "Email body"}
            },
            "required": ["to", "subject", "body"]
        }

    def _create_emitter(self) -> ToolEmitter:
        return ToolEmitter()

    async def _run(self, to: str, subject: str, body: str, **kwargs) -> Dict[str, Any]:
        """Execute the send email tool."""
        result = await self.email_server.send_email(to, subject, body)
        return result

class EmailMCPServer:
    """Email management server with WebSocket push updates."""
    def __init__(self):
        self.emails = [
            {"email_id": 1, "subject": "Meeting Tomorrow", "body": "Hi, let's meet at 10 AM."},
            {"email_id": 2, "subject": "Project Update", "body": "The project is on track."}
        ]
        self.clients = set()
        self.tools = []

    async def start(self):
        """Start the email server."""
        # Create tools
        self.tools = [
            EmailListTool(self),
            SendEmailTool(self)
        ]

    async def stop(self):
        """Stop the email server."""
        pass

    async def list_emails(self):
        """List all emails."""
        return self.emails

    async def send_email(self, to, subject, body):
        """Send an email."""
        return {"status": "sent", "to": to, "subject": subject, "body": body}

    async def send_email_tool(self, to: str, subject: str, body: str, **kwargs):
        """Tool wrapper for sending emails."""
        result = await self.send_email(to, subject, body)
        return result

    async def add_email(self, subject, body):
        """Add a new email and notify clients."""
        email_id = len(self.emails) + 1
        new_email = {"email_id": email_id, "subject": subject, "body": body}
        self.emails.append(new_email)
        await self.notify_clients(new_email)
        return new_email

    async def notify_clients(self, email):
        """Notify all WebSocket clients of a new email."""
        if self.clients:
            await asyncio.gather(*[client.send(json.dumps({"type": "email", "data": email})) for client in self.clients])

    async def websocket_handler(self, websocket, _path):
        """Handle WebSocket connections for email updates."""
        self.clients.add(websocket)
        try:
            async for message in websocket:
                data = json.loads(message)
                if data.get("type") == "new_email":
                    await self.add_email(data["subject"], data["body"])
        finally:
            self.clients.remove(websocket)

    async def start_websocket_listener(self, _agent):
        """Start the WebSocket server for email updates."""
        async with websockets.serve(lambda ws, path: self.websocket_handler(ws, path), "localhost", 8767):
            await asyncio.Future()  # Run forever

    async def get_tools(self):
        """Get tools for the agent."""
        return self.tools