import json
import async<PERSON>
import websockets
from typing import Dict, Any
from beeai_framework.tools import Tool
from beeai_framework.emitter import Emitter

class TaskListTool(Tool):
    """Tool for listing tasks."""

    def __init__(self, task_server):
        self.task_server = task_server
        super().__init__()

    @property
    def name(self) -> str:
        return "list_tasks"

    @property
    def description(self) -> str:
        return "List all tasks"

    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {},
            "required": []
        }

    def _create_emitter(self) -> Emitter:
        return Emitter()

    async def _run(self, **kwargs) -> Dict[str, Any]:
        """Execute the task listing tool."""
        tasks = await self.task_server.list_tasks()
        return {"tasks": tasks}

class AddTaskTool(Tool):
    """Tool for adding new tasks."""

    def __init__(self, task_server):
        self.task_server = task_server
        super().__init__()

    @property
    def name(self) -> str:
        return "add_task"

    @property
    def description(self) -> str:
        return "Add a new task"

    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "description": {"type": "string", "description": "Task description"},
                "assignee": {"type": "string", "description": "Task assignee"}
            },
            "required": ["description", "assignee"]
        }

    def _create_emitter(self) -> Emitter:
        return Emitter()

    async def _run(self, description: str, assignee: str, **kwargs) -> Dict[str, Any]:
        """Execute the add task tool."""
        task = await self.task_server.add_task(description, assignee)
        return {"task": task, "status": "added"}

class TaskMCPServer:
    """Task management server with WebSocket push updates."""
    def __init__(self):
        self.tasks = [
            {"task_id": 1, "description": "Complete project report", "assignee": "user"},
            {"task_id": 2, "description": "Review code", "assignee": "other"}
        ]
        self.clients = set()
        self.tools = []

    async def start(self):
        """Start the task server."""
        # Create tools
        self.tools = [
            TaskListTool(self),
            AddTaskTool(self)
        ]

    async def stop(self):
        """Stop the task server."""
        pass

    async def list_tasks(self):
        """List all tasks."""
        return self.tasks

    async def add_task(self, description, assignee):
        """Add a new task and notify clients."""
        task_id = len(self.tasks) + 1
        new_task = {"task_id": task_id, "description": description, "assignee": assignee}
        self.tasks.append(new_task)
        await self.notify_clients(new_task)
        return new_task

    async def add_task_tool(self, description: str, assignee: str, **kwargs):
        """Tool wrapper for adding tasks."""
        task = await self.add_task(description, assignee)
        return {"task": task, "status": "added"}

    async def notify_clients(self, task):
        """Notify all WebSocket clients of a new task."""
        if self.clients:
            await asyncio.gather(*[client.send(json.dumps({"type": "task", "data": task})) for client in self.clients])

    async def websocket_handler(self, websocket, _path):
        """Handle WebSocket connections for task updates."""
        self.clients.add(websocket)
        try:
            async for message in websocket:
                data = json.loads(message)
                if data.get("type") == "new_task":
                    await self.add_task(data["description"], data["assignee"])
        finally:
            self.clients.remove(websocket)

    async def start_websocket_listener(self, _agent):
        """Start the WebSocket server for task updates."""
        async with websockets.serve(lambda ws, path: self.websocket_handler(ws, path), "localhost", 8766):
            await asyncio.Future()  # Run forever

    async def get_tools(self):
        """Get tools for the agent."""
        return self.tools