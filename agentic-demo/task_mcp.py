from mcp import StdioServerParameters, ClientSession, stdio_client
from beeai_framework.tools.mcp import MCPTool
import json
import asyncio
import websockets

class TaskMCPServer:
    """Simulated MCP server for task list with WebSocket push updates."""
    def __init__(self):
        self.tasks = [
            {"task_id": 1, "description": "Complete project report", "assignee": "user"},
            {"task_id": 2, "description": "Review code", "assignee": "other"}
        ]
        self.session = None
        self.clients = set()

    async def start(self):
        """Start the simulated MCP server."""
        self.server_params = StdioServerParameters(
            command="echo",
            args=["{\"tools\": [{\"name\": \"list_tasks\", \"description\": \"List all tasks\", \"input_schema\": {}}]}"]
        )
        async with stdio_client(self.server_params) as (read, write):
            self.session = ClientSession(read, write)
            await self.session.initialize()

    async def stop(self):
        """Stop the MCP server."""
        if self.session:
            await self.session.close()

    async def list_tasks(self):
        """Simulate listing tasks."""
        return self.tasks

    async def add_task(self, description, assignee):
        """Simulate adding a new task and notify clients."""
        task_id = len(self.tasks) + 1
        new_task = {"task_id": task_id, "description": description, "assignee": assignee}
        self.tasks.append(new_task)
        await self.notify_clients(new_task)

    async def notify_clients(self, task):
        """Notify all WebSocket clients of a new task."""
        if self.clients:
            await asyncio.gather(*[client.send(json.dumps({"type": "task", "data": task})) for client in self.clients])

    async def websocket_handler(self, websocket, path):
        """Handle WebSocket connections for task updates."""
        self.clients.add(websocket)
        try:
            async for message in websocket:
                data = json.loads(message)
                if data.get("type") == "new_task":
                    await self.add_task(data["description"], data["assignee"])
        finally:
            self.clients.remove(websocket)

    async def start_websocket_listener(self, agent):
        """Start the WebSocket server for task updates."""
        async with websockets.serve(lambda ws, path: self.websocket_handler(ws, path), "localhost", 8766):
            await asyncio.Future()  # Run forever

    async def get_tools(self):
        """Get MCP tools for the agent."""
        return await MCPTool.from_client(self.session, self.server_params)