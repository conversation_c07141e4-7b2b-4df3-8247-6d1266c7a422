# Agentic AI Configuration
app:
  name: "Autonomous Agentic AI"
  version: "1.0.0"
  log_level: "INFO"

# LLM Configuration
llm:
  provider: "ollama"
  model: "llama3.1"
  temperature: 0.1
  max_tokens: 2000

# MCP Servers Configuration
mcp_servers:
  task_server:
    name: "TaskMCPServer"
    module: "servers.task_server"
    class: "TaskMCPServer"
    websocket_port: 8766
    enabled: true
    config:
      initial_tasks:
        - task_id: 1
          description: "Complete project report"
          assignee: "user"
        - task_id: 2
          description: "Review code"
          assignee: "other"

  email_server:
    name: "EmailMCPServer"
    module: "servers.email_server"
    class: "EmailMCPServer"
    websocket_port: 8767
    enabled: true
    config:
      initial_emails:
        - email_id: 1
          subject: "Meeting Tomorrow"
          body: "Hi, let's meet at 10 AM."
        - email_id: 2
          subject: "Project Update"
          body: "The project is on track."

  biz_stats_server:
    name: "BizStatsMCPServer"
    module: "servers.biz_stats_server"
    class: "BizStatsMCPServer"
    websocket_port: 8768
    enabled: true
    config:
      initial_stats:
        - month: "2025-01"
          revenue: 100000
          sales_volume: 500
        - month: "2025-02"
          revenue: 95000
          sales_volume: 480
        - month: "2025-03"
          revenue: 110000
          sales_volume: 520

# Agent Configuration
agents:
  main_agent:
    name: "ProactiveAgent"
    module: "agents.proactive_agent"
    class: "ProactiveAgent"
    enabled: true
    config:
      autonomous_triggers:
        - type: "new_task"
          condition: "assignee == 'user'"
          action: "notify_user"
        - type: "new_email"
          condition: "always"
          action: "offer_summary"
        - type: "new_stats"
          condition: "always"
          action: "offer_analysis"
        - type: "revenue_decline"
          condition: "revenue < previous_revenue * 0.95"
          action: "generate_alert"
      
      websocket_retry:
        max_retries: 5
        retry_delay: 2
        backoff_multiplier: 1.5

# WebSocket Configuration
websocket:
  user_interface_port: 8765
  host: "localhost"
  ping_interval: 30
  ping_timeout: 10

# Health Monitoring
health:
  check_interval: 30
  timeout: 5
  endpoints:
    - name: "user_interface"
      url: "ws://localhost:8765"
    - name: "task_server"
      url: "ws://localhost:8766"
    - name: "email_server"
      url: "ws://localhost:8767"
    - name: "biz_stats_server"
      url: "ws://localhost:8768"

# Autonomous Behavior
autonomous:
  enabled: true
  decision_interval: 10  # seconds
  proactive_actions:
    - name: "daily_summary"
      schedule: "0 9 * * *"  # 9 AM daily
      action: "generate_daily_summary"
    - name: "weekly_report"
      schedule: "0 9 * * 1"  # 9 AM Monday
      action: "generate_weekly_report"
  
  learning:
    enabled: true
    feedback_collection: true
    adaptation_threshold: 0.8
