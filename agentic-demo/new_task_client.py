import asyncio
import websockets
import json

async def send_new_task():
    """Simulate sending a new task to the task MCP server."""
    async with websockets.connect("ws://localhost:8766") as websocket:
        await websocket.send(json.dumps({
            "type": "new_task",
            "description": "Prepare presentation",
            "assignee": "user"
        }))
        print("Sent new task")

if __name__ == "__main__":
    asyncio.run(send_new_task())