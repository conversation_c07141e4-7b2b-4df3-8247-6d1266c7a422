#!/usr/bin/env python3
"""
Demo client for the Autonomous Agentic AI System

This client demonstrates how to interact with the agentic AI system:
1. Connect to the user interface WebSocket
2. Send requests to list data from MCP servers
3. Add new data to trigger autonomous behavior
4. Request AI-generated reports

Usage:
    python3 demo_client.py
"""

import asyncio
import websockets
import json
import sys

class AgenticAIClient:
    """Client for interacting with the Agentic AI system."""
    
    def __init__(self, uri="ws://localhost:8765"):
        self.uri = uri
        self.websocket = None
    
    async def connect(self):
        """Connect to the agentic AI system."""
        try:
            self.websocket = await websockets.connect(self.uri)
            print(f"✅ Connected to Agentic AI System at {self.uri}")
            return True
        except Exception as e:
            print(f"❌ Failed to connect: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from the system."""
        if self.websocket:
            await self.websocket.close()
            print("👋 Disconnected from Agentic AI System")
    
    async def send_request(self, request_type, **kwargs):
        """Send a request to the system."""
        if not self.websocket:
            print("❌ Not connected to the system")
            return None
        
        request = {"type": request_type, **kwargs}
        await self.websocket.send(json.dumps(request))
        
        # Wait for response
        try:
            response = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
            return json.loads(response)
        except asyncio.TimeoutError:
            print("⏰ Request timed out")
            return None
        except Exception as e:
            print(f"❌ Error receiving response: {e}")
            return None
    
    async def list_tasks(self):
        """List all tasks."""
        print("\n📋 Listing tasks...")
        response = await self.send_request("list_tasks")
        if response and response.get("type") == "tasks":
            tasks = response.get("data", [])
            print(f"Found {len(tasks)} tasks:")
            for task in tasks:
                print(f"  • Task {task.get('task_id')}: {task.get('description')} (assigned to: {task.get('assignee')})")
        else:
            print("❌ Failed to get tasks")
    
    async def list_emails(self):
        """List all emails."""
        print("\n📧 Listing emails...")
        response = await self.send_request("list_emails")
        if response and response.get("type") == "emails":
            emails = response.get("data", [])
            print(f"Found {len(emails)} emails:")
            for email in emails:
                print(f"  • Email {email.get('email_id')}: {email.get('subject')}")
        else:
            print("❌ Failed to get emails")
    
    async def get_stats(self):
        """Get business statistics."""
        print("\n📊 Getting business statistics...")
        response = await self.send_request("get_stats")
        if response and response.get("type") == "stats":
            stats = response.get("data", [])
            print(f"Found {len(stats)} stat entries:")
            for stat in stats:
                print(f"  • {stat.get('month')}: Revenue ${stat.get('revenue'):,}, Sales Volume {stat.get('sales_volume')}")
        else:
            print("❌ Failed to get stats")
    
    async def generate_report(self):
        """Request an AI-generated business report."""
        print("\n🤖 Requesting AI-generated business report...")
        response = await self.send_request("generate_stats_report")
        if response and response.get("type") == "report":
            report = response.get("data", "")
            print("📄 AI-Generated Business Report:")
            print("=" * 50)
            print(report)
            print("=" * 50)
        else:
            print("❌ Failed to generate report")
    
    async def listen_for_notifications(self):
        """Listen for autonomous notifications from the AI."""
        print("\n👂 Listening for autonomous AI notifications...")
        print("(The AI will notify you when new data is added or important events occur)")
        
        try:
            while True:
                message = await self.websocket.recv()
                data = json.loads(message)
                
                if data.get("type") == "notification":
                    print(f"\n🔔 AI Notification: {data.get('message')}")
                elif data.get("type") == "error":
                    print(f"\n❌ Error: {data.get('message')}")
                else:
                    print(f"\n📨 Received: {data}")
                    
        except websockets.exceptions.ConnectionClosed:
            print("\n🔌 Connection closed")
        except KeyboardInterrupt:
            print("\n⏹️ Stopped listening")

async def demo_autonomous_behavior():
    """Demonstrate autonomous behavior by adding data to MCP servers."""
    print("\n🎭 Demonstrating Autonomous Behavior")
    print("=" * 50)
    
    # Connect to individual MCP servers to trigger autonomous behavior
    servers = [
        ("Task Server", "ws://localhost:8766"),
        ("Email Server", "ws://localhost:8767"),
        ("Business Stats Server", "ws://localhost:8768")
    ]
    
    for server_name, uri in servers:
        try:
            print(f"\n📡 Connecting to {server_name}...")
            async with websockets.connect(uri) as websocket:
                print(f"✅ Connected to {server_name}")
                
                if "Task" in server_name:
                    # Add a new task assigned to user (should trigger notification)
                    await websocket.send(json.dumps({
                        "type": "new_task",
                        "description": "Review quarterly performance metrics",
                        "assignee": "user"
                    }))
                    print("📋 Added new task assigned to user")
                
                elif "Email" in server_name:
                    # Add a new email (should trigger notification)
                    await websocket.send(json.dumps({
                        "type": "new_email",
                        "subject": "Urgent: Budget Review Required",
                        "body": "Please review the Q4 budget proposal by end of week."
                    }))
                    print("📧 Added new urgent email")
                
                elif "Stats" in server_name:
                    # Add stats with revenue decline (should trigger alert)
                    await websocket.send(json.dumps({
                        "type": "new_stats",
                        "month": "2025-04",
                        "revenue": 85000,  # Lower than previous months
                        "sales_volume": 420
                    }))
                    print("📊 Added new stats with revenue decline")
                
                # Small delay to let the server process
                await asyncio.sleep(0.5)
                
        except Exception as e:
            print(f"❌ Failed to connect to {server_name}: {e}")

async def interactive_demo():
    """Run an interactive demo."""
    client = AgenticAIClient()
    
    if not await client.connect():
        return
    
    try:
        print("\n🎉 Welcome to the Autonomous Agentic AI Demo!")
        print("=" * 50)
        
        # Show current data
        await client.list_tasks()
        await client.list_emails()
        await client.get_stats()
        
        # Generate initial report
        await client.generate_report()
        
        # Start listening for notifications in background
        listen_task = asyncio.create_task(client.listen_for_notifications())
        
        # Demonstrate autonomous behavior
        await asyncio.sleep(2)  # Give time for listener to start
        await demo_autonomous_behavior()
        
        print("\n⏰ Waiting for autonomous AI notifications...")
        print("(Press Ctrl+C to exit)")
        
        # Wait for user to interrupt
        await listen_task
        
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    finally:
        await client.disconnect()

async def main():
    """Main demo function."""
    print("🤖 Autonomous Agentic AI System - Demo Client")
    print("=" * 60)
    print("This demo will:")
    print("1. Connect to the AI system")
    print("2. Show current data from all MCP servers")
    print("3. Generate an AI business report")
    print("4. Trigger autonomous behavior by adding new data")
    print("5. Show real-time AI notifications")
    print()
    
    try:
        await interactive_demo()
    except Exception as e:
        print(f"💥 Demo failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo terminated by user")
    except Exception as e:
        print(f"\n💥 Demo error: {e}")
        sys.exit(1)
