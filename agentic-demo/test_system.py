#!/usr/bin/env python3
"""Simple test script to verify the agentic AI system works."""

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_basic_imports():
    """Test basic imports."""
    print("🧪 Testing basic imports...")
    
    try:
        import yaml
        print("✅ YAML imported")
        
        from core.config import config
        print("✅ Config imported")
        
        from core.base import BaseMCPServer, BaseAgent
        print("✅ Base classes imported")
        
        from servers.task_server import TaskMCPServer
        print("✅ Task server imported")
        
        from agents.proactive_agent import ProactiveAgent
        print("✅ Proactive agent imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_config_loading():
    """Test configuration loading."""
    print("\n🧪 Testing configuration loading...")
    
    try:
        from core.config import config
        
        # Test basic config access
        app_name = config.get("app.name")
        print(f"✅ App name: {app_name}")
        
        # Test MCP servers config
        servers = config.get_mcp_servers()
        print(f"✅ Found {len(servers)} MCP servers configured")
        
        # Test agents config
        agents = config.get_agents()
        print(f"✅ Found {len(agents)} agents configured")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_server_creation():
    """Test MCP server creation."""
    print("\n🧪 Testing MCP server creation...")
    
    try:
        from servers.task_server import TaskMCPServer
        from core.config import config
        
        # Get task server config
        server_config = config.get("mcp_servers.task_server")
        if not server_config:
            print("❌ No task server config found")
            return False
        
        # Create task server
        task_server = TaskMCPServer("task_server", server_config)
        print("✅ Task server created")
        
        # Initialize server
        await task_server.initialize()
        print("✅ Task server initialized")
        
        # Create tools
        tools = await task_server.create_tools()
        print(f"✅ Created {len(tools)} tools")
        
        return True
        
    except Exception as e:
        print(f"❌ Server creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_creation():
    """Test agent creation."""
    print("\n🧪 Testing agent creation...")
    
    try:
        from agents.proactive_agent import ProactiveAgent
        from core.config import config
        
        # Get agent config
        agent_config = config.get("agents.main_agent")
        if not agent_config:
            print("❌ No main agent config found")
            return False
        
        # Create agent
        agent = ProactiveAgent("main_agent", agent_config)
        print("✅ Proactive agent created")
        
        # Initialize agent
        await agent.initialize()
        print("✅ Agent initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting Agentic AI System Tests\n")
    
    tests = [
        test_basic_imports,
        test_config_loading,
        test_server_creation,
        test_agent_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if await test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is ready.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test runner failed: {e}")
        sys.exit(1)
