import json
import asyncio
import websockets
from typing import Dict, Any
from beeai_framework.tools import Tool
from beeai_framework.tools.events import ToolEmitter

class GetStatsTool(Tool):
    """Tool for getting business statistics."""

    def __init__(self, stats_server):
        self.stats_server = stats_server
        super().__init__()

    @property
    def name(self) -> str:
        return "get_stats"

    @property
    def description(self) -> str:
        return "Get business statistics"

    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {},
            "required": []
        }

    def _create_emitter(self) -> ToolEmitter:
        return ToolEmitter()

    async def _run(self, **kwargs) -> Dict[str, Any]:
        """Execute the get stats tool."""
        stats = await self.stats_server.get_stats()
        return {"stats": stats}

class AddStatsTool(Tool):
    """Tool for adding new business statistics."""

    def __init__(self, stats_server):
        self.stats_server = stats_server
        super().__init__()

    @property
    def name(self) -> str:
        return "add_stats"

    @property
    def description(self) -> str:
        return "Add new business statistics"

    @property
    def input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "month": {"type": "string", "description": "Month in YYYY-MM format"},
                "revenue": {"type": "number", "description": "Revenue amount"},
                "sales_volume": {"type": "number", "description": "Sales volume"}
            },
            "required": ["month", "revenue", "sales_volume"]
        }

    def _create_emitter(self) -> ToolEmitter:
        return ToolEmitter()

    async def _run(self, month: str, revenue: float, sales_volume: int, **kwargs) -> Dict[str, Any]:
        """Execute the add stats tool."""
        stats = await self.stats_server.add_stats(month, revenue, sales_volume)
        return {"stats": stats, "status": "added"}

class BizStatsMCPServer:
    """Business statistics server with WebSocket push updates."""
    def __init__(self):
        self.stats = [
            {"month": "2025-01", "revenue": 100000, "sales_volume": 500},
            {"month": "2025-02", "revenue": 95000, "sales_volume": 480},
            {"month": "2025-03", "revenue": 110000, "sales_volume": 520}
        ]
        self.clients = set()
        self.tools = []

    async def start(self):
        """Start the stats server."""
        # Create tools
        self.tools = [
            SimpleTool("get_stats", "Get business statistics", self.get_stats),
            SimpleTool("add_stats", "Add new business statistics", self.add_stats_tool)
        ]

    async def stop(self):
        """Stop the stats server."""
        pass

    async def get_stats(self):
        """Get business statistics."""
        return self.stats

    async def add_stats(self, month, revenue, sales_volume):
        """Add new business stats and notify clients."""
        new_stats = {"month": month, "revenue": revenue, "sales_volume": sales_volume}
        self.stats.append(new_stats)
        await self.notify_clients(new_stats)
        return new_stats

    async def add_stats_tool(self, month: str, revenue: float, sales_volume: int, **kwargs):
        """Tool wrapper for adding stats."""
        stats = await self.add_stats(month, revenue, sales_volume)
        return {"stats": stats, "status": "added"}

    async def notify_clients(self, stats):
        """Notify all WebSocket clients of new stats."""
        if self.clients:
            await asyncio.gather(*[client.send(json.dumps({"type": "stats", "data": stats})) for client in self.clients])

    async def websocket_handler(self, websocket, _path):
        """Handle WebSocket connections for stats updates."""
        self.clients.add(websocket)
        try:
            async for message in websocket:
                data = json.loads(message)
                if data.get("type") == "new_stats":
                    await self.add_stats(data["month"], data["revenue"], data["sales_volume"])
        finally:
            self.clients.remove(websocket)

    async def start_websocket_listener(self, _agent):
        """Start the WebSocket server for stats updates."""
        async with websockets.serve(lambda ws, path: self.websocket_handler(ws, path), "localhost", 8768):
            await asyncio.Future()  # Run forever

    async def get_tools(self):
        """Get tools for the agent."""
        return self.tools