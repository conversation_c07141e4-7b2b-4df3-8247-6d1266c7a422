from mcp import StdioServerParameters, ClientSession, stdio_client
from beeai_framework.tools.mcp_tools import MCPTool
import json
import asyncio
import websockets

class BizStatsMCPServer:
    """Simulated MCP server for business statistics with WebSocket push updates."""
    def __init__(self):
        self.stats = [
            {"month": "2025-01", "revenue": 100000, "sales_volume": 500},
            {"month": "2025-02", "revenue": 95000, "sales_volume": 480},
            {"month": "2025-03", "revenue": 110000, "sales_volume": 520}
        ]
        self.session = None
        self.clients = set()

    async def start(self):
        """Start the simulated MCP server."""
        self.server_params = StdioServerParameters(
            command="echo",
            args=["{\"tools\": [{\"name\": \"get_stats\", \"description\": \"Get business statistics\", \"input_schema\": {}}]}"]
        )
        async with stdio_client(self.server_params) as (read, write):
            self.session = ClientSession(read, write)
            await self.session.initialize()

    async def stop(self):
        """Stop the MCP server."""
        if self.session:
            await self.session.close()

    async def get_stats(self):
        """Simulate retrieving business statistics."""
        return self.stats

    async def add_stats(self, month, revenue, sales_volume):
        """Simulate adding new business stats and notify clients."""
        new_stats = {"month": month, "revenue": revenue, "sales_volume": sales_volume}
        self.stats.append(new_stats)
        await self.notify_clients(new_stats)

    async def notify_clients(self, stats):
        """Notify all WebSocket clients of new stats."""
        if self.clients:
            await asyncio.gather(*[client.send(json.dumps({"type": "stats", "data": stats})) for client in self.clients])

    async def websocket_handler(self, websocket, path):
        """Handle WebSocket connections for stats updates."""
        self.clients.add(websocket)
        try:
            async for message in websocket:
                data = json.loads(message)
                if data.get("type") == "new_stats":
                    await self.add_stats(data["month"], data["revenue"], data["sales_volume"])
        finally:
            self.clients.remove(websocket)

    async def start_websocket_listener(self, agent):
        """Start the WebSocket server for stats updates."""
        async with websockets.serve(lambda ws, path: self.websocket_handler(ws, path), "localhost", 8768):
            await asyncio.Future()  # Run forever

    async def get_tools(self):
        """Get MCP tools for the agent."""
        return await MCPTool.from_client(self.session, self.server_params)